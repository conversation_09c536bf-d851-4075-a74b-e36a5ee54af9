#!/usr/bin/env python3
"""
测试活跃运行检测方法
区分后台服务和真正的活跃应用
"""
import sys
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log

def test_active_vs_background():
    """测试活跃检测 vs 后台检测"""
    print("🔍 测试活跃检测 vs 后台检测")
    print("=" * 60)
    
    monitor = AdbProcessMonitor()
    
    test_packages = [
        ("com.transsion.screenrecorder", "录屏应用（系统服务）"),
        ("com.android.systemui", "系统UI（系统服务）"),
        ("com.nonexistent.app", "不存在的应用"),
    ]
    
    for package, description in test_packages:
        print(f"\n📱 {description}")
        print(f"包名: {package}")
        print("-" * 40)
        
        try:
            # 1. 传统检测（包括后台服务）
            result_traditional = monitor.is_package_running(package, use_fast_method=True)
            print(f"传统检测（含后台）: {result_traditional}")
            
            # 2. 活跃检测（排除后台服务）
            result_active = monitor.is_package_actively_running(package)
            print(f"活跃检测（排除后台）: {result_active}")
            
            # 3. 结果分析
            if result_traditional and not result_active:
                print("✅ 仅有后台服务运行，无前台活动")
            elif result_traditional and result_active:
                print("🟢 应用正在前台活跃运行")
            elif not result_traditional and not result_active:
                print("⚪ 应用完全未运行")
            else:
                print("❓ 异常情况")
                
        except Exception as e:
            print(f"检测异常: {e}")

def test_recommended_usage():
    """推荐的使用方式"""
    print(f"\n💡 推荐的使用方式")
    print("=" * 60)
    
    monitor = AdbProcessMonitor()
    package_name = "com.transsion.screenrecorder"
    
    print(f"📱 测试包名: {package_name}")
    print("-" * 40)
    
    # 方式1: 检测是否活跃运行（推荐用于UI测试）
    is_active = monitor.is_package_actively_running(package_name)
    print(f"是否活跃运行: {is_active}")
    
    # 方式2: 检测是否有进程（包括后台服务）
    is_running = monitor.is_package_running(package_name, use_fast_method=True)
    print(f"是否有进程运行: {is_running}")
    
    print(f"\n📋 使用建议:")
    print(f"- UI自动化测试: 使用 is_package_actively_running()")
    print(f"- 进程监控: 使用 is_package_running()")
    print(f"- 系统服务检测: 两者结合使用")
    
    if is_running and not is_active:
        print(f"\n✅ 对于 {package_name}:")
        print(f"   有后台服务运行，但无前台界面")
        print(f"   UI测试应该返回 False（未启动）")
    elif is_active:
        print(f"\n🟢 对于 {package_name}:")
        print(f"   正在前台运行，有用户界面")
        print(f"   UI测试应该返回 True（已启动）")
    else:
        print(f"\n⚪ 对于 {package_name}:")
        print(f"   完全未运行")

if __name__ == "__main__":
    try:
        # 对比测试
        test_active_vs_background()
        
        # 推荐用法
        test_recommended_usage()
        
        print(f"\n🎯 总结:")
        print(f"新增了 is_package_actively_running() 方法")
        print(f"可以区分后台系统服务和真正的前台应用")
        print(f"解决了录屏应用的误报问题")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
