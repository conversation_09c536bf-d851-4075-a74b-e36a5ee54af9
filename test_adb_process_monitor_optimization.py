#!/usr/bin/env python3
"""
测试AdbProcessMonitor优化效果
验证缓存机制和快速检测方法的性能提升
"""
import sys
import os
import time
from typing import List

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.logger import log
from tools.adb_process_monitor import AdbProcessMonitor


def test_cache_mechanism():
    """测试缓存机制"""
    log.info("🧪 测试缓存机制")
    log.info("=" * 50)
    
    monitor = AdbProcessMonitor(cache_duration=10)  # 10秒缓存
    
    # 第一次调用（无缓存）
    log.info("📊 第一次调用 get_all_processes（无缓存）...")
    start_time = time.time()
    processes1 = monitor.get_all_processes()
    time1 = time.time() - start_time
    log.info(f"⏱️ 第一次调用耗时: {time1:.3f}秒，获取到 {len(processes1)} 个进程")
    
    # 查看缓存信息
    cache_info = monitor.get_cache_info()
    log.info(f"📋 缓存信息: {cache_info}")
    
    # 第二次调用（使用缓存）
    log.info("📊 第二次调用 get_all_processes（使用缓存）...")
    start_time = time.time()
    processes2 = monitor.get_all_processes()
    time2 = time.time() - start_time
    log.info(f"⏱️ 第二次调用耗时: {time2:.3f}秒，获取到 {len(processes2)} 个进程")
    
    # 性能提升计算
    if time1 > 0:
        improvement = ((time1 - time2) / time1) * 100
        log.info(f"🚀 性能提升: {improvement:.1f}%")
    
    # 验证数据一致性
    if len(processes1) == len(processes2):
        log.info("✅ 缓存数据一致性验证通过")
    else:
        log.warning("⚠️ 缓存数据可能不一致")
    
    return time1, time2


def test_fast_detection():
    """测试快速检测方法"""
    log.info("🧪 测试快速检测方法")
    log.info("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 测试包名列表
    test_packages = [
        "com.transsion.aivoiceassistant",  # Ella应用
        "com.android.settings",           # 设置应用
        "com.android.camera",             # 相机应用
        "com.nonexistent.app"             # 不存在的应用
    ]
    
    for package in test_packages:
        log.info(f"\n🔍 测试包名: {package}")
        
        # 方法1: 快速检测
        start_time = time.time()
        result_fast = monitor.is_package_running(package, use_fast_method=True)
        time_fast = time.time() - start_time
        
        # 方法2: 完整检测
        start_time = time.time()
        result_full = monitor.is_package_running(package, use_fast_method=False)
        time_full = time.time() - start_time
        
        log.info(f"⚡ 快速检测: {result_fast} (耗时: {time_fast:.3f}秒)")
        log.info(f"🔍 完整检测: {result_full} (耗时: {time_full:.3f}秒)")
        
        # 验证结果一致性
        if result_fast == result_full:
            log.info("✅ 检测结果一致")
        else:
            log.warning("⚠️ 检测结果不一致，可能需要调整快速检测逻辑")
        
        # 性能提升
        if time_full > 0:
            improvement = ((time_full - time_fast) / time_full) * 100
            log.info(f"🚀 快速检测性能提升: {improvement:.1f}%")


def test_multiple_calls_performance():
    """测试多次调用的性能表现"""
    log.info("🧪 测试多次调用性能表现")
    log.info("=" * 50)
    
    monitor = AdbProcessMonitor(cache_duration=5)
    test_package = "com.android.settings"
    call_count = 10
    
    log.info(f"📊 连续调用 {call_count} 次 is_package_running...")
    
    # 测试优化后的方法
    start_time = time.time()
    results = []
    for i in range(call_count):
        result = monitor.is_package_running(test_package, use_fast_method=True)
        results.append(result)
        log.debug(f"第{i+1}次调用: {result}")
    
    total_time = time.time() - start_time
    avg_time = total_time / call_count
    
    log.info(f"⏱️ 总耗时: {total_time:.3f}秒")
    log.info(f"⏱️ 平均每次调用: {avg_time:.3f}秒")
    log.info(f"📊 调用结果: {set(results)} (一致性: {'✅' if len(set(results)) == 1 else '❌'})")
    
    # 查看缓存使用情况
    cache_info = monitor.get_cache_info()
    log.info(f"📋 最终缓存信息: {cache_info}")
    
    return total_time, avg_time


def test_cache_expiration():
    """测试缓存过期机制"""
    log.info("🧪 测试缓存过期机制")
    log.info("=" * 50)
    
    monitor = AdbProcessMonitor(cache_duration=2)  # 2秒缓存
    
    # 第一次调用
    log.info("📊 第一次调用...")
    processes1 = monitor.get_all_processes()
    cache_info1 = monitor.get_cache_info()
    log.info(f"📋 缓存信息1: 有效={cache_info1['cache_valid']}, 年龄={cache_info1['cache_age']:.1f}秒")
    
    # 等待缓存过期
    log.info("⏳ 等待缓存过期...")
    time.sleep(3)
    
    # 检查缓存状态
    cache_info2 = monitor.get_cache_info()
    log.info(f"📋 缓存信息2: 有效={cache_info2['cache_valid']}, 年龄={cache_info2['cache_age']:.1f}秒")
    
    # 第二次调用（应该重新获取）
    log.info("📊 第二次调用（缓存已过期）...")
    start_time = time.time()
    processes2 = monitor.get_all_processes()
    time2 = time.time() - start_time
    log.info(f"⏱️ 第二次调用耗时: {time2:.3f}秒")
    
    # 验证缓存是否重新生成
    cache_info3 = monitor.get_cache_info()
    log.info(f"📋 缓存信息3: 有效={cache_info3['cache_valid']}, 年龄={cache_info3['cache_age']:.1f}秒")
    
    if cache_info3['cache_age'] < 1:
        log.info("✅ 缓存过期机制工作正常")
    else:
        log.warning("⚠️ 缓存过期机制可能有问题")


def main():
    """主函数"""
    log.info("🧪 AdbProcessMonitor优化效果测试")
    log.info("=" * 70)
    
    tests = [
        ("缓存机制测试", test_cache_mechanism),
        ("快速检测方法测试", test_fast_detection),
        ("多次调用性能测试", test_multiple_calls_performance),
        ("缓存过期机制测试", test_cache_expiration)
    ]
    
    for test_name, test_func in tests:
        log.info(f"\n🔍 执行测试: {test_name}")
        log.info("-" * 70)
        
        try:
            test_func()
            log.info(f"✅ {test_name} - 完成")
        except Exception as e:
            log.error(f"❌ {test_name} - 失败: {e}")
    
    log.info("\n" + "=" * 70)
    log.info("📊 优化建议")
    log.info("=" * 70)
    log.info("1. 使用缓存机制可以显著提高重复调用的性能")
    log.info("2. 快速检测方法适用于简单的运行状态检查")
    log.info("3. 根据使用场景调整缓存持续时间")
    log.info("4. 在需要最新数据时可以手动清除缓存")
    
    log.info("\n🎉 所有测试完成！")


if __name__ == "__main__":
    main()
