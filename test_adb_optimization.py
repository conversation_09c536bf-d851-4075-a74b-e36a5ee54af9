#!/usr/bin/env python3
"""
测试ADB功能优化后的效果
验证BaseEllaTest中的ADB相关功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root))

from testcases.test_ella.base_ella_test import BaseEllaTest
from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log


def test_adb_process_monitor():
    """测试AdbProcessMonitor基本功能"""
    print("🔍 测试AdbProcessMonitor基本功能...")
    
    try:
        # 初始化监控器
        monitor = AdbProcessMonitor(cache_duration=5)
        print("✅ AdbProcessMonitor初始化成功")
        
        # 测试获取进程列表
        processes = monitor.get_all_processes(use_cache=False)
        print(f"✅ 获取进程列表成功，共 {len(processes)} 个进程")
        
        # 测试检查包状态
        test_package = "com.android.settings"
        is_running = monitor.is_package_running(test_package)
        print(f"✅ 包状态检查成功: {test_package} -> {'运行中' if is_running else '未运行'}")
        
        # 测试缓存功能
        cache_info = monitor.get_cache_info()
        print(f"✅ 缓存信息: {cache_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ AdbProcessMonitor测试失败: {e}")
        return False


def test_base_ella_test():
    """测试BaseEllaTest优化后的功能"""
    print("\n🔍 测试BaseEllaTest优化后的功能...")
    
    try:
        # 初始化测试基类
        base_test = BaseEllaTest()
        print("✅ BaseEllaTest初始化成功")
        
        # 测试ADB监控器是否正确初始化
        assert hasattr(base_test, 'adb_monitor'), "adb_monitor属性不存在"
        assert isinstance(base_test.adb_monitor, AdbProcessMonitor), "adb_monitor类型不正确"
        print("✅ ADB监控器初始化正确")
        
        # 测试清理功能（不实际执行，只测试方法存在）
        assert hasattr(base_test, 'clear_all_running_processes'), "clear_all_running_processes方法不存在"
        assert hasattr(base_test, 'clear_recent_apps'), "clear_recent_apps方法不存在"
        print("✅ 清理方法存在且可调用")
        
        return True
        
    except Exception as e:
        print(f"❌ BaseEllaTest测试失败: {e}")
        return False


def test_integration():
    """测试集成功能"""
    print("\n🔍 测试集成功能...")
    
    try:
        base_test = BaseEllaTest()
        
        # 测试通过BaseEllaTest调用ADB功能
        monitor = base_test.adb_monitor
        
        # 测试获取缓存信息
        cache_info = monitor.get_cache_info()
        print(f"✅ 通过BaseEllaTest获取缓存信息成功: {cache_info}")
        
        # 测试快速包检查
        test_package = "com.android.systemui"
        is_running = monitor.is_package_running_fast(test_package)
        print(f"✅ 快速包检查成功: {test_package} -> {'运行中' if is_running else '未运行'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试ADB功能优化...")
    print("=" * 60)
    
    results = []
    
    # 测试AdbProcessMonitor
    results.append(test_adb_process_monitor())
    
    # 测试BaseEllaTest
    results.append(test_base_ella_test())
    
    # 测试集成功能
    results.append(test_integration())
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    test_names = [
        "AdbProcessMonitor基本功能",
        "BaseEllaTest优化功能", 
        "集成功能测试"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！ADB功能优化成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        sys.exit(1)
