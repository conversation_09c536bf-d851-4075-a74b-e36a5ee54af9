"""
测试截图检查的命令行功能
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def test_cli_functionality():
    """测试命令行功能"""
    print("🔍 测试截图检查命令行功能")
    print("=" * 50)
    
    # 直接调用方法测试
    detector = FileDetector(use_adb=True)
    
    print("1. 直接方法调用测试:")
    result = detector.check_recent_screenshot_image("/sdcard/Pictures/Screenshot", time_threshold=86400)
    print(f"   check_recent_screenshot_image: {'✅ True' if result else '❌ False'}")
    
    print("\n2. 对比相机图片检查:")
    result = detector.check_recent_camera_image(time_threshold=86400)
    print(f"   check_recent_camera_image: {'✅ True' if result else '❌ False'}")
    
    print("\n3. 推荐的命令行使用方式:")
    print("   # 检查最近的截图")
    print("   python -c \"")
    print("   from tools.file_detector import FileDetector")
    print("   detector = FileDetector(use_adb=True)")
    print("   result = detector.check_recent_screenshot_image(time_threshold=30)")
    print("   print('True' if result else 'False')")
    print("   \"")
    
    print("\n4. 自动化测试集成示例:")
    print("   # 在自动化脚本中使用")
    print("   def verify_screenshot():")
    print("       from tools.file_detector import FileDetector")
    print("       detector = FileDetector(use_adb=True)")
    print("       return detector.check_recent_screenshot_image(time_threshold=10)")

def main():
    """主函数"""
    test_cli_functionality()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    
    print("\n📋 截图检查方法总结:")
    print("方法名: check_recent_screenshot_image")
    print("参数:")
    print("  - screenshot_dir: 截图目录（默认: /sdcard/Pictures/Screenshot）")
    print("  - time_threshold: 时间阈值秒数（默认: 30）")
    print("返回值: bool（True=找到，False=未找到）")
    print("文件格式: Screenshot_YYYYMMDD-HHMMSS.jpg")
    print("示例: Screenshot_20250716-175221.jpg")

if __name__ == "__main__":
    main()
