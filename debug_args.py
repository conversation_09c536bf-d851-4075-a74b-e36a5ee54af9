"""
调试命令行参数解析
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def test_args():
    """测试参数传递"""
    print("测试参数传递")
    
    # 模拟命令行参数
    target_date = "20250726"
    
    detector = FileDetector(use_adb=True)
    results = detector.check_camera_images(target_date=target_date)
    
    print(f"指定日期: {target_date}")
    print(f"结果中的日期: {results.get('target_date')}")
    print(f"匹配文件数: {len(results.get('matched_files', []))}")
    
    if results.get('matched_files'):
        print("匹配的文件:")
        for file_info in results['matched_files']:
            print(f"  - {file_info['name']}")

if __name__ == "__main__":
    test_args()
