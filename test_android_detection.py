"""
测试Android文件检测功能
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def test_android_detection():
    """测试Android文件检测"""
    print("🤖 测试Android文件检测功能")
    print("=" * 50)
    
    # 创建Android模式检测器
    detector = FileDetector(use_adb=True)
    
    # 检查今天的照片
    today = datetime.now().strftime('%Y%m%d')
    print(f"检查日期: {today}")
    
    results = detector.check_camera_images(target_date=today)
    
    print(f"\n检测结果:")
    print(f"  目录: {results['directory']}")
    print(f"  目录存在: {'✅' if results['directory_exists'] else '❌'}")
    print(f"  总文件数: {results['total_files']}")
    print(f"  匹配文件数: {len(results['matched_files'])}")
    print(f"  设备模式: {results.get('device_mode', '未知')}")
    
    if results['matched_files']:
        print(f"\n今天拍摄的照片:")
        for i, file_info in enumerate(results['matched_files'], 1):
            print(f"  {i}. {file_info['name']}")
            print(f"     大小: {file_info['size']} bytes")
            print(f"     路径: {file_info['path']}")
            print(f"     修改时间: {file_info['modified_time']}")
            print()
    else:
        print("  今天没有拍摄照片")
    
    # 导出结果
    output_file = detector.export_results(results, "android_test_result.json")
    print(f"结果已导出到: {output_file}")
    
    return results

if __name__ == "__main__":
    test_android_detection()
