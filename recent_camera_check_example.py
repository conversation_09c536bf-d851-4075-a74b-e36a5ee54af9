"""
最近相机图片检查功能使用示例
演示如何使用 check_recent_camera_image 方法
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def example_basic_usage():
    """基础使用示例"""
    print("📱 基础使用示例")
    print("=" * 40)
    
    # 创建Android模式检测器
    detector = FileDetector(use_adb=True)
    
    # 检查默认相机目录30秒内的图片
    result = detector.check_recent_camera_image()
    
    if result:
        print("✅ 找到最近30秒内拍摄的照片！")
        print("   说明：相机功能正常工作")
    else:
        print("❌ 未找到最近30秒内拍摄的照片")
        print("   说明：可能没有拍照或相机功能异常")
    
    return result

def example_custom_threshold():
    """自定义时间阈值示例"""
    print("\n⏰ 自定义时间阈值示例")
    print("=" * 40)
    
    detector = FileDetector(use_adb=True)
    
    # 测试不同的时间阈值
    thresholds = [10, 30, 60, 300]  # 10秒、30秒、1分钟、5分钟
    
    for threshold in thresholds:
        result = detector.check_recent_camera_image(time_threshold=threshold)
        status = "✅ 找到" if result else "❌ 未找到"
        print(f"  {threshold:3d}秒内: {status}")
        
        if result:
            print(f"      → 在{threshold}秒内有拍照活动")
            break
    
    return any(detector.check_recent_camera_image(time_threshold=t) for t in thresholds)

def example_custom_directory():
    """自定义目录示例"""
    print("\n📂 自定义目录示例")
    print("=" * 40)
    
    detector = FileDetector(use_adb=True)
    
    # 测试不同的目录
    test_directories = [
        "/sdcard/DCIM/Camera",
        "/storage/emulated/0/DCIM/Camera",
        "/sdcard/Pictures"
    ]
    
    for directory in test_directories:
        result = detector.check_recent_camera_image(directory, time_threshold=60)
        status = "✅ 找到" if result else "❌ 未找到"
        print(f"  {directory}: {status}")

def example_automation_scenario():
    """自动化测试场景示例"""
    print("\n🤖 自动化测试场景示例")
    print("=" * 40)
    
    detector = FileDetector(use_adb=True)
    
    print("模拟自动化测试流程:")
    print("1. 触发相机拍照...")
    print("   # 这里可以添加触发拍照的代码")
    print("   # 例如：driver.tap(camera_button)")
    
    print("2. 等待2秒...")
    import time
    time.sleep(2)
    
    print("3. 检查是否生成了新照片...")
    result = detector.check_recent_camera_image(time_threshold=10)
    
    if result:
        print("✅ 测试通过：相机拍照功能正常")
        print("   → 在10秒内检测到新生成的照片")
    else:
        print("❌ 测试失败：相机拍照功能异常")
        print("   → 未在10秒内检测到新照片")
    
    return result

def example_local_filesystem():
    """本地文件系统示例"""
    print("\n🖥️ 本地文件系统示例")
    print("=" * 40)
    
    # 创建本地模式检测器
    detector = FileDetector()
    
    # 检查本地相机目录
    result = detector.check_recent_camera_image(r"C:\Users\<USER>\DCIM\Camera", time_threshold=30)
    
    if result:
        print("✅ 在本地相机目录找到最近的照片")
    else:
        print("❌ 在本地相机目录未找到最近的照片")
        print("   提示：请确保路径正确且有相应权限")
    
    return result

def practical_usage_tips():
    """实用使用技巧"""
    print("\n💡 实用使用技巧")
    print("=" * 40)
    
    print("1. 相机功能测试:")
    print("   if detector.check_recent_camera_image(time_threshold=30):")
    print("       print('相机工作正常')")
    print("   else:")
    print("       print('相机可能有问题')")
    
    print("\n2. 批量照片检测:")
    print("   for threshold in [10, 30, 60]:")
    print("       if detector.check_recent_camera_image(time_threshold=threshold):")
    print("           print(f'在{threshold}秒内有拍照活动')")
    print("           break")
    
    print("\n3. 多目录检查:")
    print("   directories = ['/sdcard/DCIM/Camera', '/sdcard/Pictures']")
    print("   for dir in directories:")
    print("       if detector.check_recent_camera_image(dir):")
    print("           print(f'在{dir}找到最近照片')")
    
    print("\n4. 结合自动化测试:")
    print("   # 执行拍照操作")
    print("   driver.tap(camera_button)")
    print("   time.sleep(2)")
    print("   # 验证是否成功拍照")
    print("   assert detector.check_recent_camera_image(time_threshold=10)")

def main():
    """主函数"""
    print("🔍 最近相机图片检查功能使用示例")
    print("=" * 50)
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"当前时间: {current_time}")
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_custom_threshold()
        example_custom_directory()
        example_automation_scenario()
        example_local_filesystem()
        practical_usage_tips()
        
        print("\n" + "=" * 50)
        print("✅ 所有示例运行完成！")
        
        print("\n📋 方法总结:")
        print("check_recent_camera_image(camera_dir, time_threshold)")
        print("- camera_dir: 相机目录路径（可选，默认为系统相机目录）")
        print("- time_threshold: 时间阈值（秒，默认30秒）")
        print("- 返回值: bool（True=找到最近图片，False=未找到）")
        
        print("\n🎯 适用场景:")
        print("- 自动化测试中验证相机拍照功能")
        print("- 检查应用是否成功触发拍照")
        print("- 监控相机活动状态")
        print("- 验证图片生成时间")
        
    except Exception as e:
        print(f"❌ 运行示例时发生错误: {e}")

if __name__ == "__main__":
    main()
