#!/usr/bin/env python3
"""
调试录音应用检测问题
分析为什么检测不到recorder应用
"""
import sys
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from pages.base.app_detector import AppDetector, AppType
from core.logger import log

def debug_recorder_detection():
    """调试录音应用检测"""
    print("🔍 调试录音应用检测问题")
    print("=" * 60)
    
    # 1. 检查录音应用检测器配置
    print("1️⃣ 检查录音应用检测器配置")
    print("-" * 40)
    
    app_detector = AppDetector()
    recorder_detector = app_detector.get_detector(AppType.RECORDER)
    
    if recorder_detector:
        package_names = recorder_detector.get_package_names()
        keywords = recorder_detector.get_keywords()
        
        print(f"配置的包名: {package_names}")
        print(f"配置的关键词: {keywords}")
    else:
        print("❌ 未找到录音应用检测器")
        return
    
    # 2. 检查实际运行的录音相关进程
    print(f"\n2️⃣ 检查实际运行的录音相关进程")
    print("-" * 40)
    
    monitor = AdbProcessMonitor()
    
    # 搜索所有包含record的进程
    try:
        success, ps_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "ps", "-A", "|", "grep", "-i", "record"], timeout=10
        )
        
        if success and ps_output.strip():
            print("找到包含'record'的进程:")
            lines = ps_output.strip().split('\n')
            for i, line in enumerate(lines, 1):
                print(f"  {i}. {line}")
        else:
            print("未找到包含'record'的进程")
    except Exception as e:
        print(f"搜索record进程异常: {e}")
    
    # 搜索所有包含sound的进程
    try:
        success, ps_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "ps", "-A", "|", "grep", "-i", "sound"], timeout=10
        )
        
        if success and ps_output.strip():
            print("\n找到包含'sound'的进程:")
            lines = ps_output.strip().split('\n')
            for i, line in enumerate(lines, 1):
                print(f"  {i}. {line}")
        else:
            print("\n未找到包含'sound'的进程")
    except Exception as e:
        print(f"搜索sound进程异常: {e}")
    
    # 3. 逐个测试配置的包名
    print(f"\n3️⃣ 逐个测试配置的包名")
    print("-" * 40)
    
    for package in package_names:
        print(f"\n📱 测试包名: {package}")
        
        # 使用pidof检测
        try:
            success, pidof_output = monitor.detector_utils.execute_adb_command(
                ["adb", "shell", "pidof", package], timeout=3
            )
            print(f"  pidof 结果: {'找到' if (success and pidof_output.strip()) else '未找到'}")
            if success and pidof_output.strip():
                print(f"  PID: {pidof_output.strip()}")
        except Exception as e:
            print(f"  pidof 异常: {e}")
        
        # 使用ps检测
        try:
            success, ps_output = monitor.detector_utils.execute_adb_command(
                ["adb", "shell", "ps", "-A", "|", "grep", package], timeout=5
            )
            print(f"  ps 结果: {'找到' if (success and ps_output.strip()) else '未找到'}")
            if success and ps_output.strip():
                print(f"  进程信息: {ps_output.strip()}")
        except Exception as e:
            print(f"  ps 异常: {e}")
        
        # 使用检测器方法
        try:
            is_running = monitor.is_package_running(package, use_fast_method=True)
            print(f"  快速检测: {is_running}")
            
            is_active = monitor.is_package_actively_running(package)
            print(f"  活跃检测: {is_active}")
        except Exception as e:
            print(f"  检测器异常: {e}")
    
    # 4. 检查所有transsion应用
    print(f"\n4️⃣ 检查所有transsion应用")
    print("-" * 40)
    
    try:
        success, ps_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "ps", "-A", "|", "grep", "transsion"], timeout=10
        )
        
        if success and ps_output.strip():
            print("找到的transsion应用:")
            lines = ps_output.strip().split('\n')
            for i, line in enumerate(lines, 1):
                print(f"  {i}. {line}")
                
                # 检查是否有录音相关的应用
                if any(keyword in line.lower() for keyword in ['record', 'sound', 'audio']):
                    print(f"    ⭐ 可能是录音相关应用")
        else:
            print("未找到transsion应用")
    except Exception as e:
        print(f"搜索transsion应用异常: {e}")
    
    # 5. 使用应用检测器测试
    print(f"\n5️⃣ 使用应用检测器测试")
    print("-" * 40)
    
    try:
        is_opened = app_detector.check_app_opened(AppType.RECORDER)
        print(f"录音应用检测结果: {is_opened}")
        
        # 如果检测失败，尝试手动检测
        if not is_opened:
            print("检测失败，尝试手动分析...")
            
            # 检查是否有录音相关的Activity
            success, activity_output = monitor.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-i", "record"], timeout=10
            )
            
            if success and activity_output.strip():
                print("找到录音相关的Activity:")
                lines = activity_output.strip().split('\n')
                for line in lines[:5]:  # 只显示前5行
                    print(f"  {line}")
    
    except Exception as e:
        print(f"应用检测器测试异常: {e}")

def suggest_fixes():
    """建议修复方案"""
    print(f"\n💡 建议修复方案")
    print("=" * 60)
    
    print("基于调试结果，可能的解决方案:")
    print("1. 更新包名列表 - 添加实际运行的录音应用包名")
    print("2. 优化检测逻辑 - 改进活跃状态检测")
    print("3. 添加备用检测 - 通过Activity或服务检测")
    print("4. 检查应用状态 - 确认录音应用是否真的在运行")

if __name__ == "__main__":
    try:
        debug_recorder_detection()
        suggest_fixes()
        print(f"\n🎯 调试完成！")
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
