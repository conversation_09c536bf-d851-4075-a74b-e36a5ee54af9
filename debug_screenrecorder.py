#!/usr/bin/env python3
"""
调试 com.transsion.screenrecorder 检测问题
分析为什么未启动的应用返回 true
"""
import sys
import subprocess
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log

def debug_package_detection(package_name="com.transsion.screenrecorder"):
    """调试包检测问题"""
    print(f"🔍 调试包检测: {package_name}")
    print("=" * 60)
    
    monitor = AdbProcessMonitor()
    
    # 1. 测试快速检测方法
    print(f"\n1️⃣ 快速检测方法 (pidof)")
    print("-" * 40)
    
    try:
        success, output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "pidof", package_name], timeout=3
        )
        print(f"pidof 命令执行: {'成功' if success else '失败'}")
        print(f"pidof 输出: '{output.strip()}'")
        print(f"pidof 输出长度: {len(output.strip())}")
        print(f"pidof 判断结果: {success and output.strip()}")
        
        if success and output.strip():
            pids = output.strip().split()
            print(f"找到的PID: {pids}")
            
            # 验证每个PID
            for pid in pids:
                if pid.isdigit():
                    print(f"\n验证PID {pid}:")
                    
                    # 检查进程是否存在
                    success_proc, _ = monitor.detector_utils.execute_adb_command(
                        ["adb", "shell", "test", "-d", f"/proc/{pid}"], timeout=2
                    )
                    print(f"  进程目录存在: {success_proc}")
                    
                    # 检查进程名
                    success_comm, comm_output = monitor.detector_utils.execute_adb_command(
                        ["adb", "shell", "cat", f"/proc/{pid}/comm"], timeout=2
                    )
                    print(f"  进程名获取: {'成功' if success_comm else '失败'}")
                    if success_comm:
                        print(f"  进程名: '{comm_output.strip()}'")
                        print(f"  包名匹配: {package_name in comm_output}")
                    
                    # 检查进程状态
                    success_stat, stat_output = monitor.detector_utils.execute_adb_command(
                        ["adb", "shell", "cat", f"/proc/{pid}/stat"], timeout=2
                    )
                    if success_stat:
                        stat_parts = stat_output.split()
                        if len(stat_parts) > 2:
                            state = stat_parts[2]
                            print(f"  进程状态: {state}")
                    
                    # 检查命令行
                    success_cmdline, cmdline_output = monitor.detector_utils.execute_adb_command(
                        ["adb", "shell", "cat", f"/proc/{pid}/cmdline"], timeout=2
                    )
                    if success_cmdline:
                        cmdline = cmdline_output.replace('\x00', ' ').strip()
                        print(f"  命令行: '{cmdline}'")
                        print(f"  命令行包含包名: {package_name in cmdline}")
        
    except Exception as e:
        print(f"pidof 检测异常: {e}")
    
    # 2. 测试ps命令
    print(f"\n2️⃣ ps 命令检测")
    print("-" * 40)
    
    try:
        success, output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "ps", "-A"], timeout=10
        )
        
        if success:
            lines = output.split('\n')
            matching_lines = []
            for line in lines:
                if package_name in line:
                    matching_lines.append(line.strip())
            
            print(f"ps 命令找到 {len(matching_lines)} 个匹配行:")
            for i, line in enumerate(matching_lines, 1):
                print(f"  {i}. {line}")
                
                # 分析每一行
                parts = line.split()
                if len(parts) >= 8:
                    user = parts[0]
                    pid = parts[1]
                    state = parts[7]
                    name = ' '.join(parts[8:])
                    print(f"     用户: {user}, PID: {pid}, 状态: {state}, 名称: {name}")
                    print(f"     精确匹配: {name == package_name}")
        else:
            print(f"ps 命令执行失败: {output}")
            
    except Exception as e:
        print(f"ps 检测异常: {e}")
    
    # 3. 测试完整检测方法
    print(f"\n3️⃣ 完整检测方法")
    print("-" * 40)
    
    try:
        result_fast = monitor.is_package_running(package_name, use_fast_method=True)
        print(f"快速检测结果: {result_fast}")
        
        result_full = monitor.is_package_running(package_name, use_fast_method=False)
        print(f"完整检测结果: {result_full}")
        
        print(f"结果一致: {result_fast == result_full}")
        
    except Exception as e:
        print(f"完整检测异常: {e}")
    
    # 4. 检查相似包名
    print(f"\n4️⃣ 检查相似包名")
    print("-" * 40)
    
    try:
        success, output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "ps", "-A", "|", "grep", "screen"], timeout=5
        )
        
        if success and output.strip():
            print("包含 'screen' 的进程:")
            lines = output.strip().split('\n')
            for line in lines:
                print(f"  {line}")
        else:
            print("未找到包含 'screen' 的进程")
            
    except Exception as e:
        print(f"相似包名检查异常: {e}")

def test_other_packages():
    """测试其他包名作为对比"""
    print(f"\n🔬 对比测试其他包名")
    print("=" * 60)
    
    monitor = AdbProcessMonitor()
    
    test_packages = [
        "com.android.systemui",      # 通常运行的系统应用
        "com.nonexistent.fake.app",  # 明确不存在的应用
        "com.transsion.screenrecorder",  # 问题应用
    ]
    
    for package in test_packages:
        print(f"\n📱 测试: {package}")
        try:
            result = monitor.is_package_running(package, use_fast_method=True)
            print(f"  检测结果: {result}")
        except Exception as e:
            print(f"  检测异常: {e}")

if __name__ == "__main__":
    try:
        debug_package_detection()
        test_other_packages()
        print(f"\n🎯 调试完成！")
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
