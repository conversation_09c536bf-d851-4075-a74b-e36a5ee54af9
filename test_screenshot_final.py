"""
测试修复后的截图检查功能
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def test_screenshot_check():
    """测试截图检查功能"""
    print("📱 测试Android设备截图检查功能")
    print("=" * 50)
    
    # 创建Android模式检测器
    detector = FileDetector(use_adb=True)
    
    print("1. 测试默认截图目录:")
    result = detector.check_recent_screenshot_image()
    print(f"   默认目录（30秒内）: {'✅ True' if result else '❌ False'}")
    
    print("\n2. 测试实际存在的截图目录:")
    result = detector.check_recent_screenshot_image("/sdcard/Pictures/Screenshot", time_threshold=86400)  # 24小时内
    print(f"   /sdcard/Pictures/Screenshot（24小时内）: {'✅ True' if result else '❌ False'}")
    
    print("\n3. 测试不同时间阈值:")
    for threshold in [30, 3600, 86400]:  # 30秒、1小时、24小时
        result = detector.check_recent_screenshot_image("/sdcard/Pictures/Screenshot", time_threshold=threshold)
        if threshold < 3600:
            time_str = f"{threshold}秒"
        elif threshold < 86400:
            time_str = f"{threshold//3600}小时"
        else:
            time_str = f"{threshold//86400}天"
        print(f"   {time_str}内: {'✅ True' if result else '❌ False'}")
    
    print("\n4. 方法对比:")
    camera_result = detector.check_recent_camera_image(time_threshold=86400)
    screenshot_result = detector.check_recent_screenshot_image("/sdcard/Pictures/Screenshot", time_threshold=86400)
    print(f"   相机图片（24小时内）: {'✅ True' if camera_result else '❌ False'}")
    print(f"   截图文件（24小时内）: {'✅ True' if screenshot_result else '❌ False'}")

def demo_usage():
    """演示使用方法"""
    print("\n💡 使用方法演示")
    print("=" * 50)
    
    print("基础用法:")
    print("```python")
    print("from tools.file_detector import FileDetector")
    print("")
    print("# 创建Android模式检测器")
    print("detector = FileDetector(use_adb=True)")
    print("")
    print("# 检查默认截图目录30秒内的截图")
    print("result = detector.check_recent_screenshot_image()")
    print("if result:")
    print("    print('✅ 找到最近的截图')")
    print("else:")
    print("    print('❌ 未找到最近的截图')")
    print("")
    print("# 自定义目录和时间阈值")
    print("result = detector.check_recent_screenshot_image(")
    print("    screenshot_dir='/sdcard/Pictures/Screenshot',")
    print("    time_threshold=60  # 60秒内")
    print(")")
    print("```")
    
    print("\n自动化测试场景:")
    print("```python")
    print("def test_screenshot_function():")
    print("    # 触发截图操作（例如：音量下+电源键）")
    print("    # driver.key_event(KEYCODE_VOLUME_DOWN + KEYCODE_POWER)")
    print("    time.sleep(2)")
    print("    ")
    print("    # 验证是否成功截图")
    print("    detector = FileDetector(use_adb=True)")
    print("    result = detector.check_recent_screenshot_image(time_threshold=10)")
    print("    assert result, '截图功能测试失败'")
    print("    print('✅ 截图功能测试通过')")
    print("```")

def main():
    """主函数"""
    print("🔍 截图检查功能最终测试")
    print("=" * 60)
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"当前时间: {current_time}")
    
    try:
        test_screenshot_check()
        demo_usage()
        
        print("\n" + "=" * 60)
        print("✅ 测试完成！")
        
        print("\n📋 方法总结:")
        print("check_recent_screenshot_image(screenshot_dir, time_threshold)")
        print("- screenshot_dir: 截图目录路径（默认: /sdcard/Pictures/Screenshot）")
        print("- time_threshold: 时间阈值（秒，默认30秒）")
        print("- 返回值: bool（True=找到最近截图，False=未找到）")
        print("- 文件格式: Screenshot_YYYYMMDD-HHMMSS.jpg")
        print("- 示例文件: Screenshot_20250716-175221.jpg")
        
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")

if __name__ == "__main__":
    main()
