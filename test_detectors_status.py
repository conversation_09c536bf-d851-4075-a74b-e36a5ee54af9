#!/usr/bin/env python3
"""
检测器状态测试脚本
检查所有导入的检测器是否正常工作
"""
import sys
sys.path.append('.')

from pages.base.app_detector import AppDetector, AppType

def test_all_detectors():
    """测试所有检测器"""
    print("🔍 检测器状态测试")
    print("=" * 50)
    
    try:
        detector = AppDetector()
        print(f"✅ AppDetector 初始化成功，共加载 {len(detector._detectors)} 个检测器")
        
        success_count = 0
        error_count = 0
        missing_count = 0
        
        print("\n📋 检测器详细状态:")
        print("-" * 70)
        print(f"{'应用类型':<25} | {'状态':<10} | {'包名数':<8} | {'关键词数':<8}")
        print("-" * 70)
        
        # 测试每个应用类型
        for app_type in AppType:
            try:
                detector_instance = detector.get_detector(app_type)
                if detector_instance:
                    # 获取包名和关键词
                    package_names = detector_instance.get_package_names()
                    keywords = detector_instance.get_keywords()
                    
                    # 验证数据有效性
                    if package_names and keywords:
                        print(f"{app_type.value:<25} | {'✅ 正常':<10} | {len(package_names):<8} | {len(keywords):<8}")
                        success_count += 1
                    else:
                        print(f"{app_type.value:<25} | {'⚠️ 数据空':<10} | {len(package_names) if package_names else 0:<8} | {len(keywords) if keywords else 0:<8}")
                        error_count += 1
                else:
                    print(f"{app_type.value:<25} | {'❌ 缺失':<10} | {'0':<8} | {'0':<8}")
                    missing_count += 1
                    
            except Exception as e:
                print(f"{app_type.value:<25} | {'❌ 错误':<10} | {'0':<8} | {'0':<8} | {str(e)[:20]}...")
                error_count += 1
        
        print("-" * 70)
        print(f"\n📊 统计结果:")
        print(f"✅ 正常工作: {success_count} 个")
        print(f"⚠️ 数据问题: {error_count} 个")
        print(f"❌ 检测器缺失: {missing_count} 个")
        print(f"📱 总计: {len(AppType)} 个应用类型")
        
        if success_count == len(AppType):
            print("\n🎉 所有检测器都正常工作！")
        else:
            print(f"\n⚠️ 有 {len(AppType) - success_count} 个检测器需要修复")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_missing_detectors():
    """显示缺失的检测器"""
    print("\n🔍 检查缺失的检测器文件...")
    
    try:
        detector = AppDetector()
        
        # 检查每个AppType是否有对应的检测器
        missing_detectors = []
        
        for app_type in AppType:
            detector_instance = detector.get_detector(app_type)
            if not detector_instance:
                missing_detectors.append(app_type.value)
        
        if missing_detectors:
            print(f"\n❌ 缺失的检测器 ({len(missing_detectors)} 个):")
            for detector_name in missing_detectors:
                print(f"  - {detector_name}_detector.py")
        else:
            print("\n✅ 所有检测器文件都存在")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    test_all_detectors()
    show_missing_detectors()
