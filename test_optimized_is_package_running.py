#!/usr/bin/env python3
"""
测试优化后的is_package_running方法
验证快速检测+二次验证的效果
"""
import sys
import time
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log

def test_optimized_method():
    """测试优化后的is_package_running方法"""
    print("🔍 测试优化后的is_package_running方法")
    print("=" * 60)
    
    monitor = AdbProcessMonitor()
    
    # 测试包名列表（包含常见的系统应用和可能不存在的应用）
    test_packages = [
        "com.android.systemui",      # 系统UI（通常存在）
        "com.android.settings",      # 设置应用（通常存在）
        "com.google.android.gms",    # Google服务（可能存在）
        "com.transsion.aivoiceassistant",  # Ella应用
        "com.nonexistent.app",       # 不存在的应用
        "com.fake.package.test",     # 另一个不存在的应用
    ]
    
    print(f"📋 测试包名列表 ({len(test_packages)} 个):")
    for i, pkg in enumerate(test_packages, 1):
        print(f"  {i}. {pkg}")
    
    print("\n🚀 开始测试不同的检测模式...")
    print("-" * 60)
    
    results = {}
    
    for package in test_packages:
        print(f"\n📱 测试包: {package}")
        print("-" * 40)
        
        package_results = {}
        
        # 模式1: 仅快速检测（不进行二次验证）
        start_time = time.time()
        result_fast_only = monitor.is_package_running(package, use_fast_method=True, double_check=False)
        time_fast_only = time.time() - start_time
        package_results['fast_only'] = {'result': result_fast_only, 'time': time_fast_only}
        print(f"  🏃 仅快速检测: {result_fast_only} (耗时: {time_fast_only:.3f}s)")
        
        # 模式2: 快速检测+二次验证（推荐模式）
        start_time = time.time()
        result_optimized = monitor.is_package_running(package, use_fast_method=True, double_check=True)
        time_optimized = time.time() - start_time
        package_results['optimized'] = {'result': result_optimized, 'time': time_optimized}
        print(f"  ✅ 快速+验证: {result_optimized} (耗时: {time_optimized:.3f}s)")
        
        # 模式3: 完整检测（传统方法）
        start_time = time.time()
        result_full = monitor.is_package_running(package, use_fast_method=False)
        time_full = time.time() - start_time
        package_results['full'] = {'result': result_full, 'time': time_full}
        print(f"  🐌 完整检测: {result_full} (耗时: {time_full:.3f}s)")
        
        # 分析结果一致性
        consistency = len(set([result_fast_only, result_optimized, result_full]))
        if consistency == 1:
            print(f"  ✅ 结果一致: 所有方法都返回 {result_optimized}")
        else:
            print(f"  ⚠️ 结果不一致: 快速={result_fast_only}, 优化={result_optimized}, 完整={result_full}")
        
        results[package] = package_results
    
    # 统计分析
    print("\n📊 性能和准确性分析")
    print("=" * 60)
    
    total_packages = len(test_packages)
    fast_only_times = [results[pkg]['fast_only']['time'] for pkg in test_packages]
    optimized_times = [results[pkg]['optimized']['time'] for pkg in test_packages]
    full_times = [results[pkg]['full']['time'] for pkg in test_packages]
    
    avg_fast_only = sum(fast_only_times) / total_packages
    avg_optimized = sum(optimized_times) / total_packages
    avg_full = sum(full_times) / total_packages
    
    print(f"⏱️ 平均检测时间:")
    print(f"  仅快速检测: {avg_fast_only:.3f}s")
    print(f"  快速+验证:  {avg_optimized:.3f}s")
    print(f"  完整检测:   {avg_full:.3f}s")
    
    print(f"\n🚀 性能提升:")
    print(f"  优化方法比完整检测快: {((avg_full - avg_optimized) / avg_full * 100):.1f}%")
    print(f"  仅快速比完整检测快: {((avg_full - avg_fast_only) / avg_full * 100):.1f}%")
    
    # 准确性分析
    consistent_count = 0
    for package in test_packages:
        results_set = set([
            results[package]['fast_only']['result'],
            results[package]['optimized']['result'],
            results[package]['full']['result']
        ])
        if len(results_set) == 1:
            consistent_count += 1
    
    accuracy = (consistent_count / total_packages) * 100
    print(f"\n🎯 结果一致性: {consistent_count}/{total_packages} ({accuracy:.1f}%)")
    
    # 推荐使用方式
    print(f"\n💡 推荐使用方式:")
    print(f"  monitor.is_package_running(package_name, use_fast_method=True, double_check=True)")
    print(f"  - 平衡了速度和准确性")
    print(f"  - 比完整检测快 {((avg_full - avg_optimized) / avg_full * 100):.1f}%")
    print(f"  - 提供了二次验证保障")

def test_edge_cases():
    """测试边缘情况"""
    print(f"\n🧪 测试边缘情况")
    print("=" * 60)
    
    monitor = AdbProcessMonitor()
    
    edge_cases = [
        "",                          # 空字符串
        "com",                       # 过短的包名
        "com.very.long.package.name.that.might.not.exist.anywhere.on.device",  # 超长包名
        "com.android.systemui.test", # 部分匹配的包名
    ]
    
    for package in edge_cases:
        print(f"\n📱 边缘测试: '{package}'")
        try:
            result = monitor.is_package_running(package, use_fast_method=True, double_check=True)
            print(f"  结果: {result}")
        except Exception as e:
            print(f"  异常: {e}")

if __name__ == "__main__":
    try:
        test_optimized_method()
        test_edge_cases()
        print(f"\n🎉 测试完成！")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
