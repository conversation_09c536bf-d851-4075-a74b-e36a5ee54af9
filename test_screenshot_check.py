"""
测试截图检查功能
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def test_android_screenshot_check():
    """测试Android设备截图检查"""
    print("📱 测试Android设备截图检查")
    print("=" * 50)
    
    # 创建Android模式检测器
    detector = FileDetector(use_adb=True)
    
    print("1. 测试默认截图目录（30秒内）:")
    result = detector.check_recent_screenshot_image()
    print(f"   结果: {'✅ True' if result else '❌ False'}")
    
    print("\n2. 测试不同时间阈值:")
    for threshold in [10, 30, 60, 300]:
        result = detector.check_recent_screenshot_image(time_threshold=threshold)
        print(f"   {threshold:3d}秒内: {'✅ True' if result else '❌ False'}")
    
    print("\n3. 测试不同的Android截图目录:")
    screenshot_dirs = [
        "/sdcard/Pictures/Screenshots",
        "/storage/emulated/0/Pictures/Screenshots",
        "/sdcard/DCIM/Screenshots"
    ]
    
    for screenshot_dir in screenshot_dirs:
        result = detector.check_recent_screenshot_image(screenshot_dir, time_threshold=60)
        print(f"   {screenshot_dir}: {'✅ True' if result else '❌ False'}")

def test_local_screenshot_check():
    """测试本地文件系统截图检查"""
    print("\n🖥️ 测试本地文件系统截图检查")
    print("=" * 50)
    
    # 创建本地模式检测器
    detector = FileDetector()
    
    print("1. 测试本地截图目录:")
    result = detector.check_recent_screenshot_image(r"C:\Users\<USER>\\Users\\Screenshots: {'✅ True' if result else '❌ False'}")
    
    print("\n2. 测试tools目录（作为测试）:")
    result = detector.check_recent_screenshot_image("tools", time_threshold=60)
    print(f"   tools目录: {'✅ True' if result else '❌ False'}")

def demo_usage_scenarios():
    """演示使用场景"""
    print("\n💡 使用场景演示")
    print("=" * 50)
    
    print("场景1: 自动化测试 - 验证截图功能")
    print("```python")
    print("def test_screenshot_function():")
    print("    # 触发截图操作")
    print("    # driver.key_event(KEYCODE_POWER + KEYCODE_VOLUME_DOWN)")
    print("    time.sleep(2)")
    print("    ")
    print("    # 验证是否成功截图")
    print("    detector = FileDetector(use_adb=True)")
    print("    result = detector.check_recent_screenshot_image(time_threshold=10)")
    print("    assert result, '截图功能测试失败'")
    print("```")
    
    print("\n场景2: 实时监控 - 检查截图活动")
    print("```python")
    print("def monitor_screenshot_activity():")
    print("    detector = FileDetector(use_adb=True)")
    print("    while True:")
    print("        if detector.check_recent_screenshot_image(time_threshold=30):")
    print("            print('📸 检测到新的截图活动')")
    print("        time.sleep(10)  # 每10秒检查一次")
    print("```")
    
    print("\n场景3: 功能验证 - 检查应用截图")
    print("```python")
    print("def verify_app_screenshot():")
    print("    # 应用内触发截图")
    print("    # app.screenshot_button.click()")
    print("    ")
    print("    # 检查是否生成截图")
    print("    detector = FileDetector(use_adb=True)")
    print("    if detector.check_recent_screenshot_image(time_threshold=5):")
    print("        print('✅ 应用截图功能正常')")
    print("    else:")
    print("        print('❌ 应用截图功能异常')")
    print("```")

def compare_methods():
    """对比两种检查方法"""
    print("\n🔄 方法对比")
    print("=" * 50)
    
    detector = FileDetector(use_adb=True)
    
    print("相机图片检查 vs 截图检查:")
    
    # 检查相机图片
    camera_result = detector.check_recent_camera_image(time_threshold=60)
    print(f"相机图片（60秒内）: {'✅ True' if camera_result else '❌ False'}")
    
    # 检查截图
    screenshot_result = detector.check_recent_screenshot_image(time_threshold=60)
    print(f"截图文件（60秒内）: {'✅ True' if screenshot_result else '❌ False'}")
    
    print("\n文件名格式对比:")
    print("相机图片: IMG_YYYYMMDD_HHMMSS_SSS.jpg")
    print("截图文件: Screenshot_YYYYMMDD-HHMMSS.jpg")
    
    print("\n默认目录对比:")
    print("相机图片: /sdcard/DCIM/Camera")
    print("截图文件: /sdcard/Pictures/Screenshots")

def main():
    """主函数"""
    print("🔍 截图检查功能测试")
    print("=" * 60)
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"当前时间: {current_time}")
    
    try:
        # 测试Android设备截图检查
        test_android_screenshot_check()
        
        # 测试本地文件系统截图检查
        test_local_screenshot_check()
        
        # 演示使用场景
        demo_usage_scenarios()
        
        # 对比两种方法
        compare_methods()
        
        print("\n" + "=" * 60)
        print("✅ 测试完成！")
        
        print("\n📋 新方法总结:")
        print("check_recent_screenshot_image(screenshot_dir, time_threshold)")
        print("- screenshot_dir: 截图目录路径（默认: /sdcard/Pictures/Screenshots）")
        print("- time_threshold: 时间阈值（秒，默认30秒）")
        print("- 返回值: bool（True=找到最近截图，False=未找到）")
        print("- 文件格式: Screenshot_YYYYMMDD-HHMMSS.jpg")
        
        print("\n🎯 适用场景:")
        print("- 自动化测试中验证截图功能")
        print("- 检查应用是否成功触发截图")
        print("- 监控截图活动状态")
        print("- 验证截图生成时间")
        
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")

if __name__ == "__main__":
    main()
