"""
Ella对话页面 - 集成弹窗处理功能 - 适配UIAutomator2
继承EllaDialoguePage并添加智能弹窗处理
"""
import time
from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


class EllaDialoguePageWithPopup(EllaDialoguePage):
    """Ella对话页面 - 集成弹窗处理 - 适配UIAutomator2"""

    def __init__(self, device_id: str = None):
        # 初始化父类
        super().__init__()

        # 初始化弹窗处理
        from core.popup_monitor import PopupMonitor, PopupConfig
        self.popup_config = PopupConfig()
        self.popup_monitor = PopupMonitor(self.driver, self.popup_config)
        self.auto_handle_popups = True
        self._popup_monitoring_enabled = False

        # 配置弹窗处理回调
        self._setup_popup_callbacks()

        # 配置Ella特有的弹窗处理
        self._configure_ella_popup_handling()

        # 启用弹窗监控
        self.enable_popup_monitoring()

    def _setup_popup_callbacks(self):
        """设置弹窗处理回调"""
        def on_popup_detected(popup_info):
            log.debug(f"检测到弹窗: {popup_info.type} (置信度: {popup_info.confidence:.2f})")

        def on_popup_handled(popup_info, result):
            if result.success:
                log.info(f"✅ 成功处理弹窗: {popup_info.type} - {result.method}")
            else:
                log.warning(f"❌ 处理弹窗失败: {popup_info.type} - {result.error}")

        def on_monitoring_error(error):
            log.error(f"弹窗监控错误: {error}")

        self.popup_monitor.set_callback('popup_detected', on_popup_detected)
        self.popup_monitor.set_callback('popup_handled', on_popup_handled)
        self.popup_monitor.set_callback('monitoring_error', on_monitoring_error)

    def _configure_ella_popup_handling(self):
        """配置Ella特有的弹窗处理规则"""
        # 添加Ella应用特有的弹窗识别规则
        ella_popup_rules = [
            {
                'type': 'permission',
                'patterns': ['允许', '权限', 'Allow', 'Permission'],
                'action': 'allow'
            },
            {
                'type': 'update',
                'patterns': ['更新', 'Update', '升级'],
                'action': 'dismiss'
            },
            {
                'type': 'welcome',
                'patterns': ['欢迎', 'Welcome', '首次使用'],
                'action': 'dismiss'
            }
        ]
        
        for rule in ella_popup_rules:
            self.popup_config.add_rule(rule)

    def enable_popup_monitoring(self):
        """启用弹窗监控"""
        if not self._popup_monitoring_enabled:
            self.popup_monitor.start_monitoring()
            self._popup_monitoring_enabled = True
            log.info("✅ 弹窗监控已启用")

    def disable_popup_monitoring(self):
        """禁用弹窗监控"""
        if self._popup_monitoring_enabled:
            self.popup_monitor.stop_monitoring()
            self._popup_monitoring_enabled = False
            log.info("⏹️ 弹窗监控已禁用")

    def handle_popups_immediately(self):
        """立即处理当前屏幕上的弹窗"""
        try:
            return self.popup_monitor.handle_current_popups()
        except Exception as e:
            log.error(f"立即处理弹窗失败: {e}")
            return False

    def start_app(self) -> bool:
        """启动Ella应用 - 处理启动时的弹窗"""
        try:
            log.info("🚀 启动Ella应用...")

            # 调用父类启动方法
            if not super().start_app_with_activity():
                return False
            
            # 启动后可能出现首次使用弹窗、权限弹窗等
            time.sleep(2)
            self.handle_popups_immediately()
            
            # 等待页面加载，期间处理可能的弹窗
            if self.wait_for_page_load(timeout=20):
                log.info("✅ Ella应用启动成功")
                return True
            else:
                log.error("❌ Ella应用启动超时")
                return False
                
        except Exception as e:
            log.error(f"❌ Ella应用启动失败: {e}")
            self.screenshot("ella_start_failed.png")
            return False

    def execute_text_command(self, command: str, wait_for_response: bool = True) -> bool:
        """执行文本命令 - 自动处理弹窗"""
        try:
            # 执行命令前处理弹窗
            self.handle_popups_immediately()
            
            # 调用父类方法执行命令
            result = super().execute_text_command(command, wait_for_response)
            
            # 执行命令后处理可能出现的弹窗
            time.sleep(1)
            self.handle_popups_immediately()
            
            return result
            
        except Exception as e:
            log.error(f"执行命令失败: {e}")
            return False

    def wait_for_response(self, timeout: int = 30) -> bool:
        """等待响应 - 期间处理弹窗"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                # 定期处理弹窗
                self.handle_popups_immediately()
                
                # 检查是否有响应
                if super().wait_for_response(timeout=2):
                    return True
                
                time.sleep(1)
            
            log.warning(f"等待响应超时 ({timeout}秒)")
            return False
            
        except Exception as e:
            log.error(f"等待响应失败: {e}")
            return False

    def __del__(self):
        """析构函数 - 清理弹窗监控"""
        try:
            self.disable_popup_monitoring()
        except:
            pass
