#!/usr/bin/env python3
"""
批量修复 simple_command_test 调用的脚本
将所有只接收3个返回值的调用修改为接收4个返回值
"""

import os
import re
import glob
from pathlib import Path

def fix_file(file_path):
    """修复单个文件中的 simple_command_test 调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找需要修复的模式
        pattern = r'(\s+)(initial_status, final_status, response_text)\s*=\s*self\.simple_command_test\('
        
        # 检查是否包含需要修复的模式
        if re.search(pattern, content):
            print(f"修复文件: {file_path}")
            
            # 替换模式
            new_content = re.sub(
                pattern,
                r'\1initial_status, final_status, response_text, files_status = self.simple_command_test(',
                content
            )
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return True
        else:
            return False
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始批量修复 simple_command_test 调用...")
    
    # 查找所有需要修复的Python文件 - 递归搜索所有子目录
    test_dirs = []

    # 递归查找testcases/test_ella下的所有目录
    base_dir = "testcases/test_ella"
    if os.path.exists(base_dir):
        for root, dirs, files in os.walk(base_dir):
            # 跳过__pycache__目录
            if "__pycache__" not in root:
                test_dirs.append(root)
    
    fixed_count = 0
    total_count = 0
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            # 查找该目录下的所有Python文件
            pattern = os.path.join(test_dir, "*.py")
            files = glob.glob(pattern)
            
            for file_path in files:
                total_count += 1
                if fix_file(file_path):
                    fixed_count += 1
    
    print(f"\n✅ 修复完成!")
    print(f"📊 总共检查了 {total_count} 个文件")
    print(f"🔧 修复了 {fixed_count} 个文件")
    
    if fixed_count > 0:
        print(f"\n🎉 所有文件已成功修复!")
        print("现在所有 simple_command_test 调用都会正确接收4个返回值:")
        print("  - initial_status")
        print("  - final_status") 
        print("  - response_text")
        print("  - files_status")
    else:
        print("\n💡 没有找到需要修复的文件")

if __name__ == "__main__":
    main()
