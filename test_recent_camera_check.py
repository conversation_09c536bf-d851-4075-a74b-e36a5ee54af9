"""
测试最近相机图片检查功能
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def test_local_recent_check():
    """测试本地文件系统的最近图片检查"""
    print("🖥️ 测试本地文件系统最近图片检查")
    print("=" * 50)
    
    # 创建本地模式检测器
    detector = FileDetector()
    
    # 检查默认相机目录
    result = detector.check_recent_camera_image()
    print(f"本地相机目录检查结果: {'✅ 找到最近图片' if result else '❌ 未找到最近图片'}")
    
    # 检查tools目录（作为测试）
    result = detector.check_recent_camera_image("tools", time_threshold=60)
    print(f"tools目录检查结果: {'✅ 找到最近图片' if result else '❌ 未找到最近图片'}")

def test_android_recent_check():
    """测试Android设备的最近图片检查"""
    print("\n📱 测试Android设备最近图片检查")
    print("=" * 50)
    
    try:
        # 创建Android模式检测器
        detector = FileDetector(use_adb=True)
        
        # 检查默认相机目录
        result = detector.check_recent_camera_image()
        print(f"Android相机目录检查结果: {'✅ 找到最近图片' if result else '❌ 未找到最近图片'}")
        
        # 检查不同的时间阈值
        time_thresholds = [10, 30, 60, 300]  # 10秒、30秒、1分钟、5分钟
        
        for threshold in time_thresholds:
            result = detector.check_recent_camera_image(time_threshold=threshold)
            print(f"  {threshold}秒内: {'✅ 找到' if result else '❌ 未找到'}")
        
    except Exception as e:
        print(f"Android设备检查失败: {e}")

def test_with_custom_directory():
    """测试自定义目录"""
    print("\n📂 测试自定义目录")
    print("=" * 50)
    
    # 测试Android设备的其他目录
    try:
        detector = FileDetector(use_adb=True)
        
        # 测试不同的Android目录
        test_dirs = [
            "/sdcard/DCIM/Camera",
            "/storage/emulated/0/DCIM/Camera",
            "/sdcard/Pictures"
        ]
        
        for test_dir in test_dirs:
            result = detector.check_recent_camera_image(test_dir, time_threshold=60)
            print(f"目录 {test_dir}: {'✅ 找到最近图片' if result else '❌ 未找到最近图片'}")
            
    except Exception as e:
        print(f"自定义目录检查失败: {e}")

def demo_usage():
    """演示使用方法"""
    print("\n💡 使用方法演示")
    print("=" * 50)
    
    print("1. 基础用法 - 检查默认相机目录30秒内的图片:")
    print("   detector = FileDetector(use_adb=True)")
    print("   result = detector.check_recent_camera_image()")
    print("   print('找到最近图片' if result else '未找到最近图片')")
    
    print("\n2. 自定义时间阈值 - 检查60秒内的图片:")
    print("   result = detector.check_recent_camera_image(time_threshold=60)")
    
    print("\n3. 自定义目录 - 检查指定目录:")
    print("   result = detector.check_recent_camera_image('/sdcard/Pictures', time_threshold=30)")
    
    print("\n4. 本地文件系统:")
    print("   detector = FileDetector()  # 不使用ADB")
    print("   result = detector.check_recent_camera_image(r'C:\\Users\\<USER>\\DCIM\\Camera')")

def main():
    """主函数"""
    print("🔍 最近相机图片检查功能测试")
    print("=" * 60)
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"当前时间: {current_time}")
    
    # 测试本地文件系统
    test_local_recent_check()
    
    # 测试Android设备
    test_android_recent_check()
    
    # 测试自定义目录
    test_with_custom_directory()
    
    # 演示使用方法
    demo_usage()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！")
    
    print("\n📝 说明:")
    print("- 该方法检查指定目录下是否存在以当前日期+时间戳命名的图片")
    print("- 文件名格式: IMG_YYYYMMDD_HHMMSS_SSS.jpg")
    print("- 只检查生成时间在指定时间阈值内的文件")
    print("- 支持本地文件系统和Android设备")

if __name__ == "__main__":
    main()
