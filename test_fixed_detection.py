#!/usr/bin/env python3
"""
测试修复后的进程检测方法
验证是否解决了误报问题
"""
import sys
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log

def test_screenrecorder_detection():
    """测试录屏应用检测"""
    print("🔍 测试修复后的录屏应用检测")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    package_name = "com.transsion.screenrecorder"
    
    print(f"📱 测试包名: {package_name}")
    print("-" * 50)
    
    # 1. 快速检测
    print("\n1️⃣ 快速检测方法")
    result_fast = monitor.is_package_running(package_name, use_fast_method=True)
    print(f"快速检测结果: {result_fast}")
    
    # 2. 完整检测
    print("\n2️⃣ 完整检测方法")
    result_full = monitor.is_package_running(package_name, use_fast_method=False)
    print(f"完整检测结果: {result_full}")
    
    # 3. 结果分析
    print(f"\n📊 结果分析:")
    print(f"结果一致: {result_fast == result_full}")
    
    if result_fast or result_full:
        print("⚠️ 检测到应用正在运行")
        print("请确认应用是否真的在运行，或者是否有后台服务")
    else:
        print("✅ 确认应用未运行")
    
    return result_fast, result_full

def test_manual_verification(package_name="com.transsion.screenrecorder"):
    """手动验证进程状态"""
    print(f"\n🔧 手动验证进程状态")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 1. pidof 检查
    print(f"\n1️⃣ pidof 检查")
    try:
        success, output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "pidof", package_name], timeout=3
        )
        print(f"pidof 结果: {'成功' if success else '失败'}")
        print(f"pidof 输出: '{output.strip()}'")
        
        if success and output.strip():
            pids = output.strip().split()
            print(f"找到PID: {pids}")
            
            for pid in pids:
                if pid.isdigit():
                    print(f"\n验证PID {pid}:")
                    is_valid = monitor._verify_pid_matches_package(pid, package_name)
                    print(f"  验证结果: {is_valid}")
        
    except Exception as e:
        print(f"pidof 检查异常: {e}")
    
    # 2. ps 检查
    print(f"\n2️⃣ ps 命令检查")
    try:
        success, output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "ps", "-A", "|", "grep", package_name], timeout=5
        )
        
        if success and output.strip():
            print("ps 找到匹配进程:")
            lines = output.strip().split('\n')
            for i, line in enumerate(lines, 1):
                print(f"  {i}. {line}")
        else:
            print("ps 未找到匹配进程")
            
    except Exception as e:
        print(f"ps 检查异常: {e}")

def test_other_packages():
    """测试其他包名作为对比"""
    print(f"\n🧪 对比测试")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    test_cases = [
        ("com.android.systemui", "系统UI（通常运行）"),
        ("com.nonexistent.fake.app", "不存在的应用"),
        ("com.transsion.screenrecorder", "录屏应用（问题包）"),
    ]
    
    for package, description in test_cases:
        print(f"\n📱 {description}")
        print(f"包名: {package}")
        
        try:
            result = monitor.is_package_running(package, use_fast_method=True)
            print(f"检测结果: {result}")
        except Exception as e:
            print(f"检测异常: {e}")

if __name__ == "__main__":
    try:
        # 主要测试
        fast_result, full_result = test_screenrecorder_detection()
        
        # 详细验证
        test_manual_verification()
        
        # 对比测试
        test_other_packages()
        
        print(f"\n🎯 测试总结:")
        print(f"快速检测: {fast_result}")
        print(f"完整检测: {full_result}")
        
        if not fast_result and not full_result:
            print("✅ 修复成功！应用确实未运行，返回False")
        else:
            print("⚠️ 仍然检测到应用运行，请检查是否有后台服务")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
