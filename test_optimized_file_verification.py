"""
测试优化后的文件验证逻辑
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from testcases.test_ella.base_ella_test import BaseEllaTest
from tools.file_detector import FileDetector

def test_command_type_detection():
    """测试命令类型检测逻辑"""
    print("🔍 测试优化后的文件验证逻辑")
    print("=" * 60)
    
    # 创建测试实例
    base_test = BaseEllaTest()
    file_detector = FileDetector(use_adb=True)
    
    # 测试命令列表
    test_commands = [
        # 拍照相关命令
        ("take a photo", "拍照"),
        ("take a picture", "拍照"),
        ("capture a photo", "拍照"),
        ("snap a photo", "拍照"),
        ("open camera", "拍照"),
        ("拍照", "拍照"),
        ("照片", "拍照"),
        
        # 截图相关命令
        ("take screenshot", "截图"),
        ("screenshot", "截图"),
        ("capture screen", "截图"),
        ("screen shot", "截图"),
        ("截图", "截图"),
        ("屏幕截图", "截图"),
        
        # 屏幕录制相关命令
        ("start screen recording", "屏幕录制"),
        ("record screen", "屏幕录制"),
        ("screen recording", "屏幕录制"),
        ("video recording", "屏幕录制"),
        ("录屏", "屏幕录制"),
        ("屏幕录制", "屏幕录制"),
        
        # 其他命令
        ("open bluetooth", "其他"),
        ("play music", "其他"),
        ("set alarm", "其他")
    ]
    
    print("📋 命令类型检测结果:")
    print("-" * 60)
    
    for command, expected_type in test_commands:
        print(f"\n命令: '{command}'")
        print(f"期望类型: {expected_type}")
        
        # 调用优化后的文件验证方法
        try:
            result = base_test._detect_and_verify_files(file_detector, command)
            print(f"验证结果: {'✅ True' if result else '❌ False'}")
            
            # 根据命令类型判断是否符合预期
            if expected_type == "其他":
                expected_result = False
                status = "✅ 符合预期" if result == expected_result else "⚠️ 不符合预期"
            else:
                # 对于文件相关命令，结果取决于实际文件存在情况
                status = "📝 结果取决于实际文件"
            
            print(f"状态: {status}")
            
        except Exception as e:
            print(f"❌ 执行错误: {e}")

def demo_usage_in_test():
    """演示在测试中的使用方法"""
    print("\n" + "=" * 60)
    print("💡 在测试中的使用示例")
    print("=" * 60)
    
    print("\n1. 拍照命令测试:")
    print("```python")
    print("def test_take_photo_command(self, ella_app):")
    print("    command = 'take a photo'")
    print("    initial_status, final_status, response_text, files_status = \\")
    print("        self.execute_command_and_verify(ella_app, command, verify_files=True)")
    print("    ")
    print("    # files_status 会自动调用 check_recent_camera_image()")
    print("    assert files_status, '应该检测到新拍摄的照片'")
    print("```")
    
    print("\n2. 截图命令测试:")
    print("```python")
    print("def test_screenshot_command(self, ella_app):")
    print("    command = 'take screenshot'")
    print("    initial_status, final_status, response_text, files_status = \\")
    print("        self.execute_command_and_verify(ella_app, command, verify_files=True)")
    print("    ")
    print("    # files_status 会自动调用 check_recent_screenshot_image()")
    print("    assert files_status, '应该检测到新生成的截图'")
    print("```")
    
    print("\n3. 屏幕录制命令测试:")
    print("```python")
    print("def test_screen_recording_command(self, ella_app):")
    print("    command = 'start screen recording'")
    print("    initial_status, final_status, response_text, files_status = \\")
    print("        self.execute_command_and_verify(ella_app, command, verify_files=True)")
    print("    ")
    print("    # files_status 会自动调用 check_recent_screen_recording()")
    print("    assert files_status, '应该检测到新生成的录制文件'")
    print("```")

def show_optimization_benefits():
    """展示优化的好处"""
    print("\n" + "=" * 60)
    print("🚀 优化后的好处")
    print("=" * 60)
    
    benefits = [
        "✅ 智能命令识别 - 根据命令内容自动选择对应的文件检测方法",
        "✅ 支持多种文件类型 - 相机图片、截图、屏幕录制三种类型",
        "✅ 中英文支持 - 支持中文和英文命令关键词",
        "✅ 灵活的时间阈值 - 不同类型文件使用不同的检测时间窗口",
        "✅ 详细的日志记录 - 记录检测过程和结果，便于调试",
        "✅ 异常处理 - 完善的错误处理机制，提高测试稳定性",
        "✅ 向后兼容 - 保持原有接口不变，只是内部逻辑更智能"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print("\n📊 优化前后对比:")
    print("优化前:")
    print("  - 固定调用 check_recent_camera_image()")
    print("  - 只能检测相机图片")
    print("  - 无法根据命令类型调整")
    
    print("\n优化后:")
    print("  - 智能选择检测方法")
    print("  - 支持相机图片、截图、屏幕录制")
    print("  - 根据命令内容自动适配")
    print("  - 更准确的验证结果")

def main():
    """主函数"""
    try:
        test_command_type_detection()
        demo_usage_in_test()
        show_optimization_benefits()
        
        print("\n" + "=" * 60)
        print("✅ 文件验证逻辑优化完成！")
        
        print("\n🎯 关键改进:")
        print("1. 新增 _detect_and_verify_files() 方法")
        print("2. 支持拍照、截图、屏幕录制三种文件类型检测")
        print("3. 智能关键词匹配，支持中英文")
        print("4. 不同类型使用不同的时间阈值")
        print("5. 完善的日志记录和异常处理")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
