#!/usr/bin/env python3
"""
测试无缓存的进程检测方法
验证每次都获取最新的进程状态
"""
import sys
import time
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log

def test_no_cache_detection():
    """测试无缓存的进程检测"""
    print("🔍 测试无缓存的进程检测方法")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 测试常见的系统应用
    test_packages = [
        "com.android.systemui",      # 系统UI（通常存在）
        "com.android.settings",      # 设置应用（通常存在）
        "com.nonexistent.app",       # 不存在的应用
    ]
    
    print(f"📋 测试包名: {test_packages}")
    print("-" * 50)
    
    for package in test_packages:
        print(f"\n📱 测试包: {package}")
        
        # 测试快速检测方法
        start_time = time.time()
        result_fast = monitor.is_package_running(package, use_fast_method=True)
        time_fast = time.time() - start_time
        print(f"  🏃 快速检测: {result_fast} (耗时: {time_fast:.3f}s)")
        
        # 测试完整检测方法
        start_time = time.time()
        result_full = monitor.is_package_running(package, use_fast_method=False)
        time_full = time.time() - start_time
        print(f"  🔍 完整检测: {result_full} (耗时: {time_full:.3f}s)")
        
        # 检查结果一致性
        if result_fast == result_full:
            print(f"  ✅ 结果一致: {result_fast}")
        else:
            print(f"  ⚠️ 结果不一致: 快速={result_fast}, 完整={result_full}")

def test_cache_info():
    """测试缓存信息"""
    print(f"\n📊 缓存状态信息")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 获取缓存信息
    cache_info = monitor.get_cache_info()
    print(f"缓存状态: {cache_info}")
    
    # 清除缓存
    monitor.clear_cache()
    print("✅ 缓存已清除")
    
    # 再次获取缓存信息
    cache_info_after = monitor.get_cache_info()
    print(f"清除后缓存状态: {cache_info_after}")

if __name__ == "__main__":
    try:
        test_no_cache_detection()
        test_cache_info()
        print(f"\n🎉 测试完成！")
        print(f"\n💡 说明:")
        print(f"  - is_package_running 方法现在不使用缓存")
        print(f"  - 每次调用都获取最新的进程状态")
        print(f"  - 适合测试脚本运行前进行进程清理的场景")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
