"""
测试屏幕录制检查功能
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def test_android_screen_recording_check():
    """测试Android设备屏幕录制检查"""
    print("📱 测试Android设备屏幕录制检查")
    print("=" * 50)
    
    # 创建Android模式检测器
    detector = FileDetector(use_adb=True)
    
    print("1. 测试默认屏幕录制目录（30秒内）:")
    result = detector.check_recent_screen_recording()
    print(f"   结果: {'✅ True' if result else '❌ False'}")
    
    print("\n2. 测试不同时间阈值:")
    for threshold in [10, 30, 60, 300, 3600]:
        result = detector.check_recent_screen_recording(time_threshold=threshold)
        if threshold < 60:
            time_str = f"{threshold}秒"
        elif threshold < 3600:
            time_str = f"{threshold//60}分钟"
        else:
            time_str = f"{threshold//3600}小时"
        print(f"   {time_str}内: {'✅ True' if result else '❌ False'}")
    
    print("\n3. 测试不同的Android屏幕录制目录:")
    recording_dirs = [
        "/sdcard/Movies/ScreenRecord",
        "/sdcard/Movies",
        "/sdcard/DCIM/ScreenRecord",
        "/storage/emulated/0/Movies"
    ]
    
    for recording_dir in recording_dirs:
        result = detector.check_recent_screen_recording(recording_dir, time_threshold=3600)
        print(f"   {recording_dir}: {'✅ True' if result else '❌ False'}")

def test_local_screen_recording_check():
    """测试本地文件系统屏幕录制检查"""
    print("\n🖥️ 测试本地文件系统屏幕录制检查")
    print("=" * 50)
    
    # 创建本地模式检测器
    detector = FileDetector()
    
    print("1. 测试本地屏幕录制目录:")
    result = detector.check_recent_screen_recording(r"C:\Users\<USER>\ScreenRecord", time_threshold=3600)
    print(f"   C:\\Users\\<USER>\\ScreenRecord: {'✅ True' if result else '❌ False'}")
    
    print("\n2. 测试tools目录（作为测试）:")
    result = detector.check_recent_screen_recording("tools", time_threshold=3600)
    print(f"   tools目录: {'✅ True' if result else '❌ False'}")

def demo_usage_scenarios():
    """演示使用场景"""
    print("\n💡 使用场景演示")
    print("=" * 50)
    
    print("场景1: 自动化测试 - 验证屏幕录制功能")
    print("```python")
    print("def test_screen_recording_function():")
    print("    # 触发屏幕录制操作")
    print("    # driver.start_screen_recording()")
    print("    time.sleep(5)  # 录制5秒")
    print("    # driver.stop_screen_recording()")
    print("    ")
    print("    # 验证是否成功生成录制文件")
    print("    detector = FileDetector(use_adb=True)")
    print("    result = detector.check_recent_screen_recording(time_threshold=10)")
    print("    assert result, '屏幕录制功能测试失败'")
    print("```")
    
    print("\n场景2: 实时监控 - 检查录制活动")
    print("```python")
    print("def monitor_screen_recording_activity():")
    print("    detector = FileDetector(use_adb=True)")
    print("    while True:")
    print("        if detector.check_recent_screen_recording(time_threshold=60):")
    print("            print('🎥 检测到新的屏幕录制活动')")
    print("        time.sleep(30)  # 每30秒检查一次")
    print("```")
    
    print("\n场景3: 功能验证 - 检查应用录制")
    print("```python")
    print("def verify_app_screen_recording():")
    print("    # 应用内触发屏幕录制")
    print("    # app.start_recording_button.click()")
    print("    time.sleep(10)  # 录制10秒")
    print("    # app.stop_recording_button.click()")
    print("    ")
    print("    # 检查是否生成录制文件")
    print("    detector = FileDetector(use_adb=True)")
    print("    if detector.check_recent_screen_recording(time_threshold=15):")
    print("        print('✅ 应用屏幕录制功能正常')")
    print("    else:")
    print("        print('❌ 应用屏幕录制功能异常')")
    print("```")

def compare_all_methods():
    """对比所有检查方法"""
    print("\n🔄 三种检查方法对比")
    print("=" * 50)
    
    detector = FileDetector(use_adb=True)
    
    print("相机图片 vs 截图 vs 屏幕录制检查:")
    
    # 检查相机图片
    camera_result = detector.check_recent_camera_image(time_threshold=3600)
    print(f"相机图片（1小时内）: {'✅ True' if camera_result else '❌ False'}")
    
    # 检查截图
    screenshot_result = detector.check_recent_screenshot_image(time_threshold=3600)
    print(f"截图文件（1小时内）: {'✅ True' if screenshot_result else '❌ False'}")
    
    # 检查屏幕录制
    recording_result = detector.check_recent_screen_recording(time_threshold=3600)
    print(f"屏幕录制（1小时内）: {'✅ True' if recording_result else '❌ False'}")
    
    print("\n文件名格式对比:")
    print("相机图片: IMG_YYYYMMDD_HHMMSS_SSS.jpg")
    print("截图文件: Screenshot_YYYYMMDD-HHMMSS.jpg")
    print("屏幕录制: Screen_Recording_YYYYMMDD_HHMMSS.mp4")
    
    print("\n默认目录对比:")
    print("相机图片: /sdcard/DCIM/Camera")
    print("截图文件: /sdcard/Pictures/Screenshot")
    print("屏幕录制: /sdcard/Movies/ScreenRecord")

def main():
    """主函数"""
    print("🔍 屏幕录制检查功能测试")
    print("=" * 60)
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"当前时间: {current_time}")
    
    try:
        # 测试Android设备屏幕录制检查
        test_android_screen_recording_check()
        
        # 测试本地文件系统屏幕录制检查
        test_local_screen_recording_check()
        
        # 演示使用场景
        demo_usage_scenarios()
        
        # 对比所有方法
        compare_all_methods()
        
        print("\n" + "=" * 60)
        print("✅ 测试完成！")
        
        print("\n📋 新方法总结:")
        print("check_recent_screen_recording(recording_dir, time_threshold)")
        print("- recording_dir: 屏幕录制目录路径（默认: /sdcard/Movies/ScreenRecord）")
        print("- time_threshold: 时间阈值（秒，默认30秒）")
        print("- 返回值: bool（True=找到最近录制文件，False=未找到）")
        print("- 文件格式: Screen_Recording_YYYYMMDD_HHMMSS.mp4")
        print("- 示例文件: Screen_Recording_20250716_194406.mp4")
        
        print("\n🎯 适用场景:")
        print("- 自动化测试中验证屏幕录制功能")
        print("- 检查应用是否成功触发屏幕录制")
        print("- 监控屏幕录制活动状态")
        print("- 验证录制文件生成时间")
        
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")

if __name__ == "__main__":
    main()
