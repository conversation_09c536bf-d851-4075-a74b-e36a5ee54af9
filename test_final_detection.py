#!/usr/bin/env python3
"""
最终测试修复后的活跃检测方法
验证是否正确处理了启动状态和未启动状态
"""
import sys
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log

def test_screenrecorder_states():
    """测试录屏应用的不同状态"""
    print("🔍 测试录屏应用的活跃检测")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    package_name = "com.transsion.screenrecorder"
    
    print(f"📱 测试包名: {package_name}")
    print("-" * 50)
    
    # 1. 传统检测（包括后台服务）
    result_traditional = monitor.is_package_running(package_name, use_fast_method=True)
    print(f"传统检测（含后台）: {result_traditional}")
    
    # 2. 活跃检测（修复后）
    result_active = monitor.is_package_actively_running(package_name)
    print(f"活跃检测（修复后）: {result_active}")
    
    # 3. 状态分析
    print(f"\n📊 状态分析:")
    if result_traditional and result_active:
        print("✅ 应用正在活跃运行（有前台界面或系统级活动）")
        print("   - 适合UI测试：返回 True")
    elif result_traditional and not result_active:
        print("🔵 仅有后台服务运行，无前台活动")
        print("   - 适合UI测试：返回 False")
    elif not result_traditional and not result_active:
        print("⚪ 应用完全未运行")
        print("   - 适合UI测试：返回 False")
    else:
        print("❓ 异常情况")
    
    return result_traditional, result_active

def test_comparison_apps():
    """对比测试其他应用"""
    print(f"\n🧪 对比测试其他应用")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    test_cases = [
        ("com.android.systemui", "系统UI"),
        ("com.transsion.hilauncher", "桌面启动器"),
        ("com.nonexistent.app", "不存在的应用"),
    ]
    
    for package, description in test_cases:
        print(f"\n📱 {description} ({package})")
        
        try:
            traditional = monitor.is_package_running(package, use_fast_method=True)
            active = monitor.is_package_actively_running(package)
            
            print(f"  传统检测: {traditional}")
            print(f"  活跃检测: {active}")
            
            if traditional and active:
                print("  状态: 活跃运行")
            elif traditional and not active:
                print("  状态: 仅后台服务")
            else:
                print("  状态: 未运行")
                
        except Exception as e:
            print(f"  检测异常: {e}")

def test_usage_recommendations():
    """使用建议测试"""
    print(f"\n💡 使用建议")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    package_name = "com.transsion.screenrecorder"
    
    print(f"📱 针对 {package_name} 的使用建议:")
    print("-" * 40)
    
    # 检测结果
    is_active = monitor.is_package_actively_running(package_name)
    is_running = monitor.is_package_running(package_name, use_fast_method=True)
    
    print(f"当前状态:")
    print(f"  活跃检测: {is_active}")
    print(f"  进程检测: {is_running}")
    
    print(f"\n推荐用法:")
    print(f"```python")
    print(f"# 对于UI自动化测试（推荐）")
    print(f"if monitor.is_package_actively_running('{package_name}'):")
    print(f"    print('录屏应用已启动，有用户界面')")
    print(f"else:")
    print(f"    print('录屏应用未启动或仅后台服务')")
    print(f"")
    print(f"# 对于进程监控")
    print(f"if monitor.is_package_running('{package_name}'):")
    print(f"    print('录屏应用有进程运行（包括后台服务）')")
    print(f"```")
    
    print(f"\n✅ 修复效果:")
    if is_active:
        print(f"  - 当前应用处于启动状态，返回 True ✓")
        print(f"  - 符合预期：启动状态应该返回 True")
    else:
        print(f"  - 当前应用未启动或仅后台服务，返回 False")
        print(f"  - 如果应用确实启动了，可能需要进一步调试")

if __name__ == "__main__":
    try:
        # 主要测试
        traditional, active = test_screenrecorder_states()
        
        # 对比测试
        test_comparison_apps()
        
        # 使用建议
        test_usage_recommendations()
        
        print(f"\n🎯 测试总结:")
        print(f"传统检测: {traditional}")
        print(f"活跃检测: {active}")
        
        if active:
            print("✅ 修复成功！应用启动状态正确返回 True")
        else:
            print("⚠️ 如果应用确实启动了，可能需要进一步优化检测逻辑")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
