"""
展示包名提取优化效果的脚本
"""
import sys
from pathlib import Path
import re

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from pages.base.app_detector import AppDetector, AppType

def extract_package_names(text):
    """从包名字段中提取有效的包名"""
    if not text:
        return []
    
    text = str(text).strip()
    if text == '/' or text == '':
        return []
    
    package_names = []
    
    # 使用正则表达式匹配所有com.开头的包名
    com_packages = re.findall(r'com\.[a-zA-Z0-9._]+', text)
    
    for package in com_packages:
        # 清理包名，移除末尾可能的标点符号
        clean_package = re.sub(r'[^\w.]$', '', package)
        if clean_package and clean_package not in package_names:
            package_names.append(clean_package)
    
    return package_names

def show_extraction_examples():
    """展示包名提取的示例"""
    print("📦 包名提取优化效果展示")
    print("=" * 80)
    
    # 从Excel表格中的实际例子
    examples = [
        {
            "原始文本": "com.transsion.fmradio（核心包包名）\ncom.funbase.xradio（桌面包包名）",
            "说明": "FM收音机 - 包含中文说明的多个包名"
        },
        {
            "原始文本": "与壁纸（com.android.wallpaper）共用",
            "说明": "共用包名 - 包名在括号中"
        },
        {
            "原始文本": "com.transsion.livewallpaper.pictorial\ncom.transsion.livewallpaper.page\ncom.transsion.livewallpaper.fantasy\ncom.transsion.livewallpaper.micro\ncom.transsion.livewallpaper.wakeup_mirror\ncom.transsion.livewallpaper.volcano\ncom.transsion.livewallpaper.mondrian\ncom.transsion.livewallpaper.colorart",
            "说明": "壁纸 - 多行多个包名"
        },
        {
            "原始文本": "com.idea.questionnaire",
            "说明": "问卷 - 简单包名"
        },
        {
            "原始文本": "com.google.android.apps.messaging（Google短信）",
            "说明": "Google短信 - 包名后带中文说明"
        },
        {
            "原始文本": "/",
            "说明": "无效包名 - 斜杠"
        },
        {
            "原始文本": "",
            "说明": "空包名"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['说明']}")
        print(f"   原始文本: {repr(example['原始文本'])}")
        
        extracted = extract_package_names(example['原始文本'])
        if extracted:
            print(f"   提取结果: {len(extracted)}个包名")
            for j, pkg in enumerate(extracted, 1):
                print(f"     {j}. {pkg}")
        else:
            print(f"   提取结果: 无有效包名")

def show_detector_examples():
    """展示检测器中的实际效果"""
    print("\n\n🔍 检测器中的实际应用效果")
    print("=" * 80)
    
    detector = AppDetector()
    
    # 选择几个有代表性的检测器
    examples = [
        (AppType.FM_RADIO, "FM收音机", "复杂包名格式"),
        (AppType.WALLPAPER, "壁纸", "多个包名"),
        (AppType.QUESTIONNAIRE, "问卷", "简单包名"),
        (AppType.GOOGLE_MESSAGE, "Google短信", "带说明的包名"),
    ]
    
    for app_type, name, description in examples:
        detector_instance = detector.get_detector(app_type)
        if detector_instance:
            package_names = detector_instance.get_package_names()
            keywords = detector_instance.get_keywords()
            
            print(f"\n📱 {name} ({description})")
            print(f"   应用类型: {app_type.value}")
            print(f"   包名数量: {len(package_names)}个")
            
            # 区分Excel提取的包名和生成的包名
            excel_packages = []
            generated_packages = []
            
            for pkg in package_names:
                if any(pattern in pkg for pattern in ['transsion', 'idea', 'google', 'funbase']):
                    if not pkg.endswith(app_type.value.replace('_', '')):
                        excel_packages.append(pkg)
                    else:
                        generated_packages.append(pkg)
                else:
                    generated_packages.append(pkg)
            
            if excel_packages:
                print(f"   📋 Excel提取的包名 ({len(excel_packages)}个):")
                for pkg in excel_packages:
                    print(f"     • {pkg}")
            
            if generated_packages:
                print(f"   🔧 生成的基础包名 ({len(generated_packages)}个):")
                for pkg in generated_packages:
                    print(f"     • {pkg}")
            
            print(f"   🏷️  关键词: {keywords}")

def main():
    """主函数"""
    print("🚀 Component检测器包名提取优化展示")
    print("=" * 80)
    print("本脚本展示了优化后的包名提取功能，能够智能处理Excel表格中的复杂包名格式")
    
    # 展示提取示例
    show_extraction_examples()
    
    # 展示检测器中的实际效果
    show_detector_examples()
    
    print("\n\n✅ 优化总结:")
    print("1. 使用正则表达式精确提取com.开头的包名")
    print("2. 自动处理中文说明和特殊格式")
    print("3. 支持多行多个包名的提取")
    print("4. Excel实际包名优先，基础包名补充")
    print("5. 过滤无效值，确保包名质量")

if __name__ == "__main__":
    main()
