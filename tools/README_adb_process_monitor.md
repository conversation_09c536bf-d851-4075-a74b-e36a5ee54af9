# ADB进程监控工具

## 功能介绍

`adb_process_monitor.py` 是一个通过adb命令获取Android设备所有启动中的前台、后台应用进程的工具。

## 主要功能

- 🔍 **自动检测前台/后台进程**: 自动区分当前前台运行和后台运行的应用进程
- 📊 **详细进程信息**: 显示PID、内存使用、运行状态等详细信息
- 🔎 **智能过滤**: 支持按关键词和内存大小过滤进程
- 📁 **结果导出**: 支持将结果导出为JSON格式文件(自动存储到output目录)
- 🎯 **应用进程专注**: 自动过滤系统进程，专注于应用进程
- 🎯 **包状态检测**: 检测指定包名是否在前台或后台运行 (新增)
- ⚡ **简单运行校验**: 快速返回True/False的运行状态校验 (新增)

## 使用方法

### 基本用法

```bash
# 显示所有应用进程（前台+后台）
python tools/adb_process_monitor.py

# 只显示前台进程
python tools/adb_process_monitor.py --foreground-only

# 只显示后台进程
python tools/adb_process_monitor.py --background-only
```

### 过滤功能

```bash
# 按关键词过滤（查找包含"wechat"的进程）
python tools/adb_process_monitor.py --filter wechat

# 按内存大小过滤（显示内存使用超过50MB的进程）
python tools/adb_process_monitor.py --min-memory 50

# 组合过滤
python tools/adb_process_monitor.py --filter com.tencent --min-memory 30
```

### 导出功能

```bash
# 导出结果到JSON文件(自动存储到tools/output目录)
python tools/adb_process_monitor.py --export process_report.json

# 导出过滤后的结果
python tools/adb_process_monitor.py --filter game --export game_processes.json

# 即使提供完整路径，也只会使用文件名，存储到output目录
python tools/adb_process_monitor.py --export /some/path/report.json  # 实际存储为 tools/output/report.json
```

### 包状态检测功能 (新增)

```bash
# 检测指定包名是否在前台或后台运行
python tools/adb_process_monitor.py --check-package com.example.app

# 检测包状态并导出结果(自动存储到tools/output目录)
python tools/adb_process_monitor.py --check-package com.tencent.mm --export wechat_status.json
```

### 简单运行状态校验功能 (新增)

```bash
# 校验指定包名是否正在运行(返回True/False)
python tools/adb_process_monitor.py --is-running com.example.app

# 校验运行状态并导出结果(自动存储到tools/output目录)
python tools/adb_process_monitor.py --is-running com.tencent.mm --export running_check.json
```

## 输出示例

```
📱 前台 进程 (2 个):
================================================================================
序号   PID      包名/进程名                              内存(MB)   状态
--------------------------------------------------------------------------------
1    <USER>    <GROUP>.tencent.mm                          156.8      S
2    12346    com.tencent.mm:push                     23.4       S

📱 后台 进程 (15 个):
================================================================================
序号   PID      包名/进程名                              内存(MB)   状态
--------------------------------------------------------------------------------
1    <USER>    <GROUP>.android.chrome                      89.2       S
2    11235    com.spotify.music                       67.5       S
3    11236    com.whatsapp                            45.3       S
...

📊 统计信息:
前台进程: 2 个
后台进程: 15 个
总计: 17 个应用进程
```

### 包状态检测输出示例

```
📱 应用状态: com.tencent.mm
🟢 状态: FOREGROUND
📊 进程数量: 3 个
💾 内存使用: 156.8 MB

📋 进程详情:
序号   PID      进程名                                      状态
------------------------------------------------------------
1    <USER>    <GROUP>.tencent.mm                            S(前台)
2    12346    com.tencent.mm:push                       S(后台)
3    12347    com.tencent.mm:tools                      S(后台)
```

### 简单校验输出示例

```
📱 包名: com.android.chrome
✅ 运行状态: True
💡 应用正在运行 (前台或后台)
```

```
📱 包名: com.example.nonexistent
❌ 运行状态: False
💡 应用未运行
```

## 输出目录说明

### 自动目录管理

所有导出的文件都会自动存储在 `tools/output/` 目录中：

- 📁 **自动创建**: output目录会在首次导出时自动创建
- 🔒 **路径安全**: 即使提供完整路径，也只会使用文件名部分
- 📝 **文件命名**: 支持任意文件名，建议使用描述性名称
- 🗂️ **集中管理**: 所有导出文件统一存储，便于管理和查找

### 导出文件示例

```
tools/output/
├── chrome_processes.json      # Chrome进程信息
├── chrome_status.json         # Chrome状态检测结果
├── running_check.json         # 运行状态校验结果
└── process_report.json        # 完整进程报告
```

## 技术实现

### 核心ADB命令

- `adb shell ps -A`: 获取所有进程列表
- `adb shell dumpsys activity activities`: 获取前台Activity信息
- `adb shell dumpsys meminfo <package>`: 获取指定应用的内存使用信息

### 进程分类逻辑

1. **应用进程识别**: 通过包名格式（com.*, org.*等）识别应用进程
2. **前台进程判断**: 通过dumpsys activity获取当前前台Activity，匹配对应的进程
3. **内存信息获取**: 针对每个应用进程单独获取详细内存使用情况

### 集成特性

- 使用项目现有的 `DetectorUtils.execute_adb_command` 方法
- 集成项目日志系统 (`core.logger`)
- 遵循项目代码风格和错误处理规范

## 依赖要求

- Android设备已连接并开启USB调试
- ADB工具已安装并可用
- 设备已信任当前计算机

## 故障排除

### 常见问题

1. **无法获取进程信息**
   - 检查设备连接: `adb devices`
   - 确认USB调试已开启
   - 确认设备已信任此计算机

2. **内存信息显示N/A**
   - 某些系统进程可能无法获取内存信息
   - 这是正常现象，不影响其他功能

3. **前台进程识别不准确**
   - 某些应用可能有多个进程，工具会尽力识别主进程
   - 可以通过关键词过滤来精确查找

## 扩展功能

工具设计为可扩展的，未来可以添加：

- CPU使用率监控
- 进程启动时间
- 网络使用情况
- 实时监控模式
- 进程终止功能

## 使用场景

- 📱 **应用性能测试**: 监控测试过程中的应用进程状态
- 🔍 **问题诊断**: 查找异常进程或内存泄漏
- 📊 **性能分析**: 分析应用内存使用情况
- 🧪 **自动化测试**: 集成到测试流程中进行进程验证
