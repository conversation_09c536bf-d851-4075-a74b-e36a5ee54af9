#!/usr/bin/env python3
"""
验证pages/apps/ella/history删除后的功能完整性
测试新的EllaDialoguePage和EllaDialoguePageWithPopup类
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.logger import log


def test_ella_dialogue_page_import():
    """测试EllaDialoguePage导入"""
    log.info("🧪 测试EllaDialoguePage导入...")
    
    try:
        from pages.apps.ella.dialogue_page import EllaDialoguePage
        log.info("✅ EllaDialoguePage导入成功")
        
        # 测试实例化
        ella_page = EllaDialoguePage()
        log.info("✅ EllaDialoguePage实例化成功")
        
        # 测试基本方法是否存在
        required_methods = [
            'start_app_with_activity',
            'execute_text_command',
            'wait_for_response',
            'get_response_text',
            'check_weather_app_opened',
            'check_camera_app_opened',
            'check_contacts_app_opened'
        ]
        
        for method_name in required_methods:
            if hasattr(ella_page, method_name):
                log.info(f"✅ 方法存在: {method_name}")
            else:
                log.error(f"❌ 方法缺失: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        log.error(f"❌ EllaDialoguePage测试失败: {e}")
        return False


def test_ella_dialogue_page_with_popup_import():
    """测试EllaDialoguePageWithPopup导入"""
    log.info("🧪 测试EllaDialoguePageWithPopup导入...")
    
    try:
        from pages.apps.ella.dialogue_page_with_popup import EllaDialoguePageWithPopup
        log.info("✅ EllaDialoguePageWithPopup导入成功")
        
        # 测试实例化（可能会因为缺少依赖而失败，但导入应该成功）
        try:
            ella_page = EllaDialoguePageWithPopup()
            log.info("✅ EllaDialoguePageWithPopup实例化成功")
            
            # 测试弹窗相关方法
            popup_methods = [
                'enable_popup_monitoring',
                'disable_popup_monitoring',
                'handle_popups_immediately'
            ]
            
            for method_name in popup_methods:
                if hasattr(ella_page, method_name):
                    log.info(f"✅ 弹窗方法存在: {method_name}")
                else:
                    log.error(f"❌ 弹窗方法缺失: {method_name}")
            
        except Exception as e:
            log.warning(f"⚠️ EllaDialoguePageWithPopup实例化失败（可能是依赖问题）: {e}")
            log.info("✅ 但导入成功，说明类定义正确")
        
        return True
        
    except Exception as e:
        log.error(f"❌ EllaDialoguePageWithPopup测试失败: {e}")
        return False


def test_old_imports_removed():
    """测试旧的导入是否已被移除"""
    log.info("🧪 测试旧的导入路径是否已被移除...")
    
    try:
        # 尝试导入旧的路径，应该失败
        from pages.apps.ella.history.main_page import EllaMainPage
        log.error("❌ 旧的导入路径仍然存在！")
        return False
        
    except ImportError:
        log.info("✅ 旧的导入路径已正确移除")
        return True
        
    except Exception as e:
        log.error(f"❌ 测试旧导入时发生意外错误: {e}")
        return False


def test_updated_files_functionality():
    """测试更新后的文件功能"""
    log.info("🧪 测试更新后的文件功能...")
    
    # 测试一些关键的更新文件
    test_files = [
        "debug/ella_accessibility_fix.py",
        "debug/test_ella_input_fix.py",
        "tools/examples/ella_bluetooth_example.py"
    ]
    
    success_count = 0
    
    for file_path in test_files:
        try:
            log.info(f"📄 检查文件: {file_path}")
            
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含新的导入
                if "from pages.apps.ella.dialogue_page import EllaDialoguePage" in content:
                    log.info(f"✅ {file_path} 已正确更新导入")
                    success_count += 1
                else:
                    log.warning(f"⚠️ {file_path} 可能未正确更新")
            else:
                log.warning(f"⚠️ 文件不存在: {file_path}")
                
        except Exception as e:
            log.error(f"❌ 检查文件失败 {file_path}: {e}")
    
    log.info(f"📊 文件检查结果: {success_count}/{len(test_files)} 个文件正确更新")
    return success_count > 0


def main():
    """主函数"""
    log.info("🧪 验证pages/apps/ella/history删除后的功能完整性")
    log.info("=" * 70)
    
    tests = [
        ("EllaDialoguePage导入测试", test_ella_dialogue_page_import),
        ("EllaDialoguePageWithPopup导入测试", test_ella_dialogue_page_with_popup_import),
        ("旧导入路径移除测试", test_old_imports_removed),
        ("更新文件功能测试", test_updated_files_functionality)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        log.info(f"\n🔍 执行测试: {test_name}")
        log.info("-" * 50)
        
        try:
            if test_func():
                log.info(f"✅ {test_name} - 通过")
                passed_tests += 1
            else:
                log.error(f"❌ {test_name} - 失败")
        except Exception as e:
            log.error(f"❌ {test_name} - 异常: {e}")
    
    log.info("\n" + "=" * 70)
    log.info("📊 测试结果总结")
    log.info("=" * 70)
    log.info(f"总测试数: {total_tests}")
    log.info(f"通过测试: {passed_tests}")
    log.info(f"失败测试: {total_tests - passed_tests}")
    log.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        log.info("🎉 所有测试通过！删除操作成功完成！")
    else:
        log.warning("⚠️ 部分测试失败，可能需要进一步检查")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
