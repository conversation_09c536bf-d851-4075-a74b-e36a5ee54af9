#!/usr/bin/env python3
"""
批量替换pages.apps.ella.history引用的脚本
将EllaMainPage替换为EllaDialoguePage
"""
import os
import re
import sys
from pathlib import Path

def find_files_with_pattern(directory, pattern):
    """查找包含特定模式的文件"""
    files_found = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if pattern in content:
                            files_found.append(file_path)
                except Exception as e:
                    print(f"读取文件失败 {file_path}: {e}")
    
    return files_found

def replace_in_file(file_path, replacements):
    """在文件中执行替换操作"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        for old_pattern, new_pattern in replacements:
            if old_pattern in content:
                content = content.replace(old_pattern, new_pattern)
                changes_made.append(f"  - {old_pattern} -> {new_pattern}")
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 更新文件: {file_path}")
            for change in changes_made:
                print(change)
            return True
        else:
            print(f"⚪ 无需更新: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 处理文件失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🔄 批量替换pages.apps.ella.history引用")
    print("=" * 60)
    
    # 定义替换规则
    replacements = [
        # 导入语句替换
        ("from pages.apps.ella.dialogue_page import EllaDialoguePage", 
         "from pages.apps.ella.dialogue_page import EllaDialoguePage"),
        
        # 类名替换
        ("EllaDialoguePage()", "EllaDialoguePage()"),
        ("= EllaDialoguePage()", "= EllaDialoguePage()"),
        
        # 弹窗处理类的导入（暂时注释掉，需要手动处理）
        ("# TODO: 需要手动处理弹窗功能 - from pages.apps.ella.history.main_page_with_popup import EllaMainPageWithPopup",
         "# TODO: 需要手动处理弹窗功能 - # TODO: 需要手动处理弹窗功能 - from pages.apps.ella.history.main_page_with_popup import EllaMainPageWithPopup"),
        
        ("# TODO: 需要手动处理弹窗功能 - EllaMainPageWithPopup()", "# TODO: 需要手动处理弹窗功能 - # TODO: 需要手动处理弹窗功能 - EllaMainPageWithPopup()"),
    ]
    
    # 查找需要更新的文件
    print("🔍 查找包含引用的文件...")
    files_to_update = find_files_with_pattern(".", "pages.apps.ella.history")
    
    if not files_to_update:
        print("✅ 没有找到需要更新的文件")
        return
    
    print(f"📋 找到 {len(files_to_update)} 个文件需要更新:")
    for file_path in files_to_update:
        print(f"  - {file_path}")
    
    print("\n🔧 开始执行替换...")
    
    updated_count = 0
    for file_path in files_to_update:
        if replace_in_file(file_path, replacements):
            updated_count += 1
    
    print(f"\n📊 替换完成:")
    print(f"  - 检查文件数: {len(files_to_update)}")
    print(f"  - 更新文件数: {updated_count}")
    print(f"  - 跳过文件数: {len(files_to_update) - updated_count}")
    
    print("\n⚠️  注意事项:")
    print("1. 弹窗处理相关的引用已被注释，需要手动处理")
    print("2. 请检查更新后的文件是否正常工作")
    print("3. 建议运行测试验证功能完整性")

if __name__ == "__main__":
    main()
