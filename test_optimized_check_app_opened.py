#!/usr/bin/env python3
"""
测试优化后的check_app_opened函数
验证使用AdbProcessMonitor.is_package_running方法的效果
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.logger import log
from pages.base.app_detector import AppDetector, AppType
from pages.apps.ella.dialogue_page import EllaDialoguePage
from tools.adb_process_monitor import AdbProcessMonitor


def test_base_app_detector():
    """测试BaseAppDetector的优化"""
    log.info("=" * 60)
    log.info("🧪 测试BaseAppDetector优化")
    log.info("=" * 60)
    
    try:
        detector = AppDetector()
        
        # 测试不同类型的应用检测
        test_apps = [
            AppType.WEATHER,
            AppType.CAMERA,
            AppType.SETTINGS,
            AppType.CONTACTS
        ]
        
        for app_type in test_apps:
            log.info(f"\n🔍 测试{app_type.value}应用检测...")
            result = detector.check_app_opened(app_type)
            status = "✅ 检测到" if result else "❌ 未检测到"
            log.info(f"结果: {status} {app_type.value}应用")
            
    except Exception as e:
        log.error(f"BaseAppDetector测试失败: {e}")


def test_ella_main_page():
    """测试EllaMainPage的优化方法"""
    log.info("=" * 60)
    log.info("🧪 测试EllaMainPage优化方法")
    log.info("=" * 60)
    
    try:
        ella_page = EllaDialoguePage()
        
        # 测试优化后的方法
        test_methods = [
            ("天气应用", ella_page.check_weather_app_opened),
            ("相机应用", ella_page.check_camera_app_opened),
            ("联系人应用", ella_page.check_contacts_app_opened)
        ]
        
        for app_name, method in test_methods:
            log.info(f"\n🔍 测试{app_name}检测...")
            result = method()
            status = "✅ 检测到" if result else "❌ 未检测到"
            log.info(f"结果: {status} {app_name}")
            
    except Exception as e:
        log.error(f"EllaMainPage测试失败: {e}")


def test_adb_process_monitor_directly():
    """直接测试AdbProcessMonitor.is_package_running方法"""
    log.info("=" * 60)
    log.info("🧪 直接测试AdbProcessMonitor.is_package_running")
    log.info("=" * 60)
    
    try:
        monitor = AdbProcessMonitor()
        
        # 测试一些常见的应用包名
        test_packages = [
            "com.transsion.aivoiceassistant",  # Ella应用
            "com.android.settings",           # 设置应用
            "com.android.camera",             # 相机应用
            "com.miui.weather",               # 天气应用
            "com.sh.smart.caller",            # 联系人应用
            "com.nonexistent.app"             # 不存在的应用
        ]
        
        for package in test_packages:
            log.info(f"\n🔍 测试包名: {package}")
            result = monitor.is_package_running(package)
            status = "✅ 正在运行" if result else "❌ 未运行"
            log.info(f"结果: {status}")
            
    except Exception as e:
        log.error(f"AdbProcessMonitor直接测试失败: {e}")


def performance_comparison():
    """性能对比测试"""
    log.info("=" * 60)
    log.info("🚀 性能对比测试")
    log.info("=" * 60)
    
    import time
    
    try:
        # 测试包名
        test_package = "com.android.settings"
        
        # 方法1: 使用优化后的AdbProcessMonitor
        log.info("测试优化后的方法...")
        start_time = time.time()
        monitor = AdbProcessMonitor()
        result1 = monitor.is_package_running(test_package)
        time1 = time.time() - start_time
        log.info(f"优化方法结果: {result1}, 耗时: {time1:.3f}秒")
        
        # 方法2: 使用传统的subprocess方法
        log.info("测试传统方法...")
        start_time = time.time()
        import subprocess
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities"],
            capture_output=True,
            text=True,
            timeout=10
        )
        result2 = test_package in result.stdout if result.returncode == 0 else False
        time2 = time.time() - start_time
        log.info(f"传统方法结果: {result2}, 耗时: {time2:.3f}秒")
        
        # 性能提升计算
        if time2 > 0:
            improvement = ((time2 - time1) / time2) * 100
            log.info(f"\n🎯 性能提升: {improvement:.1f}%")
        
    except Exception as e:
        log.error(f"性能对比测试失败: {e}")


def main():
    """主函数"""
    log.info("🧪 优化后的check_app_opened函数测试")
    log.info("使用AdbProcessMonitor.is_package_running方法进行优化")
    
    # 运行各项测试
    test_adb_process_monitor_directly()
    test_base_app_detector()
    test_ella_main_page()
    performance_comparison()
    
    log.info("\n" + "=" * 60)
    log.info("✅ 所有测试完成")
    log.info("=" * 60)


if __name__ == "__main__":
    main()
