#!/usr/bin/env python3
"""
强制停止录屏应用并测试检测结果
"""
import sys
import time
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log

def force_stop_package(package_name="com.transsion.screenrecorder"):
    """强制停止指定包名的应用"""
    print(f"🛑 强制停止应用: {package_name}")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 1. 检查当前状态
    print("1️⃣ 停止前状态检查")
    result_before = monitor.is_package_running(package_name, use_fast_method=True)
    print(f"停止前检测结果: {result_before}")
    
    # 2. 强制停止应用
    print(f"\n2️⃣ 执行强制停止")
    try:
        success, output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "am", "force-stop", package_name], timeout=5
        )
        print(f"force-stop 执行: {'成功' if success else '失败'}")
        if output.strip():
            print(f"输出: {output.strip()}")
        
        # 等待一下让系统处理
        time.sleep(2)
        
    except Exception as e:
        print(f"force-stop 异常: {e}")
    
    # 3. 额外的清理方法
    print(f"\n3️⃣ 额外清理方法")
    
    # 3.1 杀死进程
    try:
        success, pidof_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "pidof", package_name], timeout=3
        )
        
        if success and pidof_output.strip():
            pids = pidof_output.strip().split()
            print(f"找到残留PID: {pids}")
            
            for pid in pids:
                if pid.isdigit():
                    print(f"尝试杀死PID {pid}")
                    success, _ = monitor.detector_utils.execute_adb_command(
                        ["adb", "shell", "kill", pid], timeout=3
                    )
                    if not success:
                        # 尝试强制杀死
                        success, _ = monitor.detector_utils.execute_adb_command(
                            ["adb", "shell", "kill", "-9", pid], timeout=3
                        )
                        print(f"强制杀死PID {pid}: {'成功' if success else '失败'}")
        else:
            print("未找到残留PID")
            
    except Exception as e:
        print(f"杀死进程异常: {e}")
    
    # 3.2 禁用后重新启用（重置应用状态）
    try:
        print(f"尝试重置应用状态...")
        
        # 禁用应用
        success, _ = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "pm", "disable-user", package_name], timeout=3
        )
        time.sleep(1)
        
        # 重新启用应用
        success, _ = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "pm", "enable", package_name], timeout=3
        )
        print("应用状态重置完成")
        
    except Exception as e:
        print(f"重置应用状态异常: {e}")
    
    # 4. 等待系统稳定
    print(f"\n4️⃣ 等待系统稳定...")
    time.sleep(3)
    
    # 5. 检查停止后状态
    print(f"\n5️⃣ 停止后状态检查")
    result_after = monitor.is_package_running(package_name, use_fast_method=True)
    print(f"停止后检测结果: {result_after}")
    
    # 6. 详细验证
    print(f"\n6️⃣ 详细验证")
    try:
        success, pidof_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "pidof", package_name], timeout=3
        )
        print(f"pidof 检查: {'找到进程' if (success and pidof_output.strip()) else '未找到进程'}")
        if success and pidof_output.strip():
            print(f"残留PID: {pidof_output.strip()}")
        
        success, ps_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "ps", "-A", "|", "grep", package_name], timeout=5
        )
        print(f"ps 检查: {'找到进程' if (success and ps_output.strip()) else '未找到进程'}")
        if success and ps_output.strip():
            print(f"ps 输出: {ps_output.strip()}")
            
    except Exception as e:
        print(f"详细验证异常: {e}")
    
    # 7. 结果总结
    print(f"\n📊 清理结果总结:")
    print(f"清理前: {result_before}")
    print(f"清理后: {result_after}")
    
    if result_before and not result_after:
        print("✅ 清理成功！应用已停止")
    elif not result_before and not result_after:
        print("ℹ️ 应用本来就未运行")
    elif result_before and result_after:
        print("⚠️ 清理失败，应用仍在运行（可能是系统保护的服务）")
    else:
        print("❓ 异常情况")
    
    return result_before, result_after

def test_detection_after_cleanup():
    """清理后测试检测功能"""
    print(f"\n🧪 清理后测试检测功能")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    package_name = "com.transsion.screenrecorder"
    
    # 多次测试确保稳定性
    for i in range(3):
        print(f"\n第 {i+1} 次测试:")
        result = monitor.is_package_running(package_name, use_fast_method=True)
        print(f"检测结果: {result}")
        time.sleep(1)

if __name__ == "__main__":
    try:
        # 强制停止并测试
        before, after = force_stop_package()
        
        # 清理后的检测测试
        test_detection_after_cleanup()
        
        print(f"\n🎯 最终结论:")
        if not after:
            print("✅ 问题已解决！应用确实已停止，检测返回False")
        else:
            print("⚠️ 应用可能有系统级服务无法停止，或者是系统保护的进程")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
