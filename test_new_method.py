"""
测试新的 check_recent_camera_image 方法
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from tools.file_detector import FileDetector

def test_new_method():
    """测试新方法"""
    print("🔍 测试 check_recent_camera_image 方法")
    print("=" * 50)
    
    # 创建Android模式检测器
    detector = FileDetector(use_adb=True)
    
    print("1. 测试默认参数（30秒内）:")
    result = detector.check_recent_camera_image()
    print(f"   结果: {'✅ True' if result else '❌ False'}")
    
    print("\n2. 测试不同时间阈值:")
    for threshold in [10, 30, 60, 300]:
        result = detector.check_recent_camera_image(time_threshold=threshold)
        print(f"   {threshold:3d}秒内: {'✅ True' if result else '❌ False'}")
    
    print("\n3. 测试自定义目录:")
    result = detector.check_recent_camera_image("/sdcard/DCIM/Camera", time_threshold=60)
    print(f"   /sdcard/DCIM/Camera: {'✅ True' if result else '❌ False'}")
    
    print("\n4. 测试本地文件系统:")
    local_detector = FileDetector()
    result = local_detector.check_recent_camera_image("tools", time_threshold=60)
    print(f"   本地tools目录: {'✅ True' if result else '❌ False'}")
    
    print("\n✅ 测试完成！")
    print("\n📋 方法说明:")
    print("check_recent_camera_image(camera_dir='', time_threshold=30)")
    print("- 检查指定目录下是否存在以当前日期+时间戳命名的图片")
    print("- 文件名格式: IMG_YYYYMMDD_HHMMSS_SSS.jpg")
    print("- 只检查生成时间在指定时间阈值内的文件")
    print("- 返回值: True=找到最近图片，False=未找到最近图片")

if __name__ == "__main__":
    test_new_method()
