#!/usr/bin/env python3
"""
调试活跃检测方法
分析为什么启动状态的应用返回false
"""
import sys
sys.path.append('.')

from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log

def debug_active_detection(package_name="com.transsion.screenrecorder"):
    """详细调试活跃检测逻辑"""
    print(f"🔍 调试活跃检测: {package_name}")
    print("=" * 60)
    
    monitor = AdbProcessMonitor()
    
    print(f"📱 当前应用应该处于启动状态")
    print("-" * 40)
    
    # 1. 检查前台Activity
    print(f"\n1️⃣ 检查前台Activity")
    try:
        success, activity_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "dumpsys", "activity", "activities"], timeout=10
        )
        
        print(f"dumpsys activity 执行: {'成功' if success else '失败'}")
        
        if success:
            lines = activity_output.split('\n')
            found_resumed = False
            found_focused = False
            
            print(f"查找包含 '{package_name}' 的行:")
            for i, line in enumerate(lines):
                if package_name in line:
                    print(f"  行 {i+1}: {line.strip()}")
                    
                    # 检查是否有RESUMED或FOCUSED标记
                    if "mResumedActivity" in line:
                        found_resumed = True
                        print(f"    ✅ 找到 mResumedActivity")
                    if "mFocusedActivity" in line:
                        found_focused = True
                        print(f"    ✅ 找到 mFocusedActivity")
            
            # 查找RESUMED状态的Activity
            print(f"\n查找RESUMED状态:")
            for i, line in enumerate(lines):
                if ("mResumedActivity" in line or "mFocusedActivity" in line):
                    print(f"  行 {i+1}: {line.strip()}")
                    if package_name in line:
                        print(f"    ✅ 匹配目标包名")
            
            print(f"\n前台Activity检测结果:")
            print(f"  找到RESUMED: {found_resumed}")
            print(f"  找到FOCUSED: {found_focused}")
        
    except Exception as e:
        print(f"前台Activity检查异常: {e}")
    
    # 2. 检查焦点窗口
    print(f"\n2️⃣ 检查焦点窗口")
    try:
        success, window_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "dumpsys", "window", "windows"], timeout=10
        )
        
        print(f"dumpsys window 执行: {'成功' if success else '失败'}")
        
        if success:
            lines = window_output.split('\n')
            found_focus = False
            
            print(f"查找焦点窗口:")
            for i, line in enumerate(lines):
                if "mCurrentFocus=" in line:
                    print(f"  行 {i+1}: {line.strip()}")
                    if package_name in line and "null" not in line:
                        found_focus = True
                        print(f"    ✅ 找到目标包名的焦点窗口")
                    break
            
            print(f"\n焦点窗口检测结果: {found_focus}")
        
    except Exception as e:
        print(f"焦点窗口检查异常: {e}")
    
    # 3. 检查最近任务
    print(f"\n3️⃣ 检查最近任务")
    try:
        success, recents_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "dumpsys", "activity", "recents"], timeout=10
        )
        
        print(f"dumpsys recents 执行: {'成功' if success else '失败'}")
        
        if success:
            lines = recents_output.split('\n')
            found_visible = False
            
            print(f"查找包含 '{package_name}' 的行:")
            for i, line in enumerate(lines):
                if package_name in line:
                    print(f"  行 {i+1}: {line.strip()}")
                    if "visible=true" in line or "mVisible=true" in line:
                        found_visible = True
                        print(f"    ✅ 找到可见标记")
            
            print(f"\n最近任务检测结果: {found_visible}")
        
    except Exception as e:
        print(f"最近任务检查异常: {e}")
    
    # 4. 检查当前前台应用
    print(f"\n4️⃣ 检查当前前台应用")
    try:
        success, current_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "dumpsys", "activity", "top"], timeout=5
        )
        
        print(f"dumpsys activity top 执行: {'成功' if success else '失败'}")
        
        if success:
            print(f"当前顶层Activity信息:")
            lines = current_output.split('\n')
            for i, line in enumerate(lines[:20]):  # 只显示前20行
                if package_name in line or "ACTIVITY" in line or "TASK" in line:
                    print(f"  行 {i+1}: {line.strip()}")
        
    except Exception as e:
        print(f"当前前台应用检查异常: {e}")
    
    # 5. 测试修复后的活跃检测方法
    print(f"\n5️⃣ 测试修复后的活跃检测方法")
    try:
        result = monitor.is_package_actively_running(package_name)
        print(f"is_package_actively_running 结果: {result}")

        # 如果仍然返回False，进行更详细的分析
        if not result:
            print(f"⚠️ 仍然返回False，进行详细分析...")

            # 检查通知状态
            success, notification_output = monitor.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "notification", "|", "grep", "-i", "record"], timeout=5
            )
            if success and notification_output.strip():
                print(f"通知中的录屏相关信息:")
                lines = notification_output.strip().split('\n')
                for line in lines[:5]:  # 只显示前5行
                    print(f"  {line}")

            # 检查媒体服务
            success, media_output = monitor.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "media.camera", "|", "grep", "-i", package_name], timeout=3
            )
            if success and media_output.strip():
                print(f"媒体服务中的相关信息:")
                print(f"  {media_output.strip()}")

    except Exception as e:
        print(f"活跃检测方法异常: {e}")
    
    # 6. 额外的检测方法
    print(f"\n6️⃣ 额外检测方法")
    try:
        # 检查应用是否在前台（另一种方法）
        success, proc_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "dumpsys", "activity", "processes"], timeout=5
        )
        
        if success and package_name in proc_output:
            print(f"在进程信息中找到包名")
            lines = proc_output.split('\n')
            for line in lines:
                if package_name in line and ("foreground" in line.lower() or "visible" in line.lower()):
                    print(f"  前台进程: {line.strip()}")
        
        # 检查任务栈
        success, tasks_output = monitor.detector_utils.execute_adb_command(
            ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-A5", "-B5", package_name], timeout=5
        )
        
        if success and tasks_output.strip():
            print(f"\n任务栈中的相关信息:")
            lines = tasks_output.strip().split('\n')
            for line in lines:
                print(f"  {line}")
        
    except Exception as e:
        print(f"额外检测异常: {e}")

if __name__ == "__main__":
    try:
        debug_active_detection()
        print(f"\n🎯 调试完成！")
        print(f"请根据上述信息分析为什么活跃检测返回false")
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
