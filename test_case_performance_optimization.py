#!/usr/bin/env python3
"""
测试用例执行过程中的性能优化验证
模拟实际测试用例中频繁调用进程检查的场景
"""
import sys
import os
import time
from typing import List

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.logger import log
from tools.adb_process_monitor import AdbProcessMonitor
from pages.apps.ella.dialogue_page import EllaDialoguePage


def simulate_test_case_execution():
    """模拟测试用例执行过程中的应用检测"""
    log.info("🧪 模拟测试用例执行过程")
    log.info("=" * 60)
    
    # 创建Ella页面实例（包含优化的AdbProcessMonitor）
    ella_page = EllaDialoguePage()
    
    # 模拟测试用例中常见的应用检测场景
    test_scenarios = [
        ("检查Ella应用状态", "com.transsion.aivoiceassistant"),
        ("检查天气应用", "com.miui.weather"),
        ("检查相机应用", "com.transsion.camera"),
        ("检查设置应用", "com.android.settings"),
        ("检查联系人应用", "com.sh.smart.caller"),
    ]
    
    total_calls = 0
    total_time = 0
    
    log.info("📊 开始模拟测试用例执行...")
    
    # 模拟一个完整的测试用例流程
    for scenario_name, package_name in test_scenarios:
        log.info(f"\n🔍 {scenario_name}")
        
        # 每个场景调用多次（模拟测试用例中的重复检查）
        scenario_calls = 5
        scenario_start = time.time()
        
        for i in range(scenario_calls):
            start_time = time.time()
            
            # 使用优化后的检测方法
            if "天气" in scenario_name:
                result = ella_page.check_weather_app_opened()
            elif "相机" in scenario_name:
                result = ella_page.check_camera_app_opened()
            elif "联系人" in scenario_name:
                result = ella_page.check_contacts_app_opened()
            else:
                # 直接使用进程监控器
                result = ella_page.process_monitor.is_package_running(package_name)
            
            call_time = time.time() - start_time
            total_calls += 1
            total_time += call_time
            
            status = "✅ 运行中" if result else "❌ 未运行"
            log.debug(f"  第{i+1}次检查: {status} (耗时: {call_time:.3f}秒)")
        
        scenario_time = time.time() - scenario_start
        avg_time = scenario_time / scenario_calls
        log.info(f"  场景总耗时: {scenario_time:.3f}秒, 平均: {avg_time:.3f}秒/次")
    
    # 统计总体性能
    avg_call_time = total_time / total_calls if total_calls > 0 else 0
    
    log.info(f"\n📊 性能统计:")
    log.info(f"  总调用次数: {total_calls}")
    log.info(f"  总耗时: {total_time:.3f}秒")
    log.info(f"  平均每次调用: {avg_call_time:.3f}秒")
    
    # 获取缓存信息
    cache_info = ella_page.process_monitor.get_cache_info()
    log.info(f"  缓存使用情况: {cache_info}")
    
    return total_time, avg_call_time


def compare_optimization_methods():
    """比较不同优化方法的性能"""
    log.info("🧪 比较不同优化方法的性能")
    log.info("=" * 60)
    
    test_package = "com.android.settings"
    test_count = 10
    
    # 方法1: 无缓存的原始方法
    log.info("📊 测试方法1: 无缓存原始方法")
    monitor1 = AdbProcessMonitor(cache_duration=0)  # 禁用缓存
    
    start_time = time.time()
    for i in range(test_count):
        result = monitor1.is_package_running(test_package, use_fast_method=False)
    time1 = time.time() - start_time
    log.info(f"  耗时: {time1:.3f}秒, 平均: {time1/test_count:.3f}秒/次")
    
    # 方法2: 启用缓存的方法
    log.info("📊 测试方法2: 启用缓存方法")
    monitor2 = AdbProcessMonitor(cache_duration=10)  # 10秒缓存
    
    start_time = time.time()
    for i in range(test_count):
        result = monitor2.is_package_running(test_package, use_fast_method=False)
    time2 = time.time() - start_time
    log.info(f"  耗时: {time2:.3f}秒, 平均: {time2/test_count:.3f}秒/次")
    
    # 方法3: 快速检测方法
    log.info("📊 测试方法3: 快速检测方法")
    monitor3 = AdbProcessMonitor()
    
    start_time = time.time()
    for i in range(test_count):
        result = monitor3.is_package_running(test_package, use_fast_method=True)
    time3 = time.time() - start_time
    log.info(f"  耗时: {time3:.3f}秒, 平均: {time3/test_count:.3f}秒/次")
    
    # 性能对比
    log.info(f"\n📊 性能对比:")
    if time1 > 0:
        improvement2 = ((time1 - time2) / time1) * 100
        improvement3 = ((time1 - time3) / time1) * 100
        log.info(f"  缓存方法性能提升: {improvement2:.1f}%")
        log.info(f"  快速检测性能提升: {improvement3:.1f}%")
    
    return time1, time2, time3


def test_real_world_scenario():
    """测试真实世界场景的性能"""
    log.info("🧪 测试真实世界场景性能")
    log.info("=" * 60)
    
    # 创建Ella页面实例
    ella_page = EllaDialoguePage()
    
    # 模拟一个完整的测试用例流程
    log.info("📱 模拟完整测试用例流程...")
    
    total_start = time.time()
    
    # 1. 启动应用前检查
    log.info("1️⃣ 启动应用前检查...")
    start_time = time.time()
    initial_status = ella_page.process_monitor.is_package_running("com.transsion.aivoiceassistant")
    check1_time = time.time() - start_time
    log.info(f"   初始状态检查: {check1_time:.3f}秒")
    
    # 2. 执行命令后检查多个应用状态
    log.info("2️⃣ 执行命令后检查多个应用状态...")
    start_time = time.time()
    
    # 检查天气应用
    weather_status = ella_page.check_weather_app_opened()
    
    # 检查相机应用
    camera_status = ella_page.check_camera_app_opened()
    
    # 检查联系人应用
    contacts_status = ella_page.check_contacts_app_opened()
    
    check2_time = time.time() - start_time
    log.info(f"   多应用状态检查: {check2_time:.3f}秒")
    
    # 3. 重复验证（模拟测试用例中的多次验证）
    log.info("3️⃣ 重复验证...")
    start_time = time.time()
    
    for i in range(5):
        ella_status = ella_page.process_monitor.is_package_running("com.transsion.aivoiceassistant")
        settings_status = ella_page.process_monitor.is_package_running("com.android.settings")
    
    check3_time = time.time() - start_time
    log.info(f"   重复验证: {check3_time:.3f}秒")
    
    total_time = time.time() - total_start
    
    log.info(f"\n📊 真实场景性能统计:")
    log.info(f"  总耗时: {total_time:.3f}秒")
    log.info(f"  初始检查: {check1_time:.3f}秒")
    log.info(f"  多应用检查: {check2_time:.3f}秒")
    log.info(f"  重复验证: {check3_time:.3f}秒")
    
    # 缓存效果分析
    cache_info = ella_page.process_monitor.get_cache_info()
    log.info(f"  缓存效果: {cache_info}")
    
    return total_time


def main():
    """主函数"""
    log.info("🧪 测试用例执行过程性能优化验证")
    log.info("=" * 70)
    
    tests = [
        ("模拟测试用例执行", simulate_test_case_execution),
        ("优化方法性能对比", compare_optimization_methods),
        ("真实世界场景测试", test_real_world_scenario)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        log.info(f"\n🔍 执行测试: {test_name}")
        log.info("-" * 70)
        
        try:
            result = test_func()
            results[test_name] = result
            log.info(f"✅ {test_name} - 完成")
        except Exception as e:
            log.error(f"❌ {test_name} - 失败: {e}")
            results[test_name] = None
    
    # 总结报告
    log.info("\n" + "=" * 70)
    log.info("📊 优化效果总结")
    log.info("=" * 70)
    
    log.info("🎯 主要优化点:")
    log.info("1. ✅ 添加了进程列表缓存机制（5-10秒缓存）")
    log.info("2. ✅ 实现了快速检测方法（使用pidof命令）")
    log.info("3. ✅ 优化了EllaDialoguePage中的应用检测方法")
    log.info("4. ✅ 减少了重复的adb命令调用")
    
    log.info("\n💡 使用建议:")
    log.info("1. 对于频繁的应用状态检查，使用缓存机制")
    log.info("2. 对于简单的运行状态检查，使用快速检测方法")
    log.info("3. 根据测试场景调整缓存持续时间")
    log.info("4. 在需要最新状态时手动清除缓存")
    
    log.info("\n🎉 性能优化验证完成！")


if __name__ == "__main__":
    main()
